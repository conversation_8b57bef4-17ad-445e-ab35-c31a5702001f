include:
  - local: .gitlab/ci/includes/shared.gitlab-ci.yml

variables:
  BUILD_IMAGE: docker:27.3.1-dind
  REGISTRY: us-docker.pkg.dev/${ARTIFACT_REGISTRY_PROJECT_ID}/${API_ARTIFACT_REGISTRY}

.mise-build: &mise-build
  - cd $UI_PROJECT_ROOT
  - mise run build:${TARGET_ENVIRONMENT}
  - cd -

# Intentionally not using .gcp-auth-base because we want to handle different SA key.json for the docker build process to different repositories
.build-common-docker-images: &build-common-docker-images
  - cd ${API_PROJECT_ROOT}
  - echo -e "section_start:`date +%s`:mise-build[collapsed=true]\r\e[0Kmise build"
  - mise run build
  # needed for our docker containers
  - mise run "sdk:generate"
  - echo -e "section_end:`date +%s`:mise-build\r\e[0K"
  - |
    if [ "$CI_COMMIT_BRANCH" == "dev" ]; then
        export RELEASE_TAG="latest"
    elif [ "$CI_COMMIT_BRANCH" == "stage" ]; then
        export RELEASE_TAG="staging"
    elif [ "$CI_COMMIT_BRANCH" == "main" ]; then
        export RELEASE_TAG="stable"
    else
        export RELEASE_TAG="testing"
    fi
  - ${CI_PROJECT_DIR}/scripts/build-docker-images.sh
  - ${CI_PROJECT_DIR}/scripts/build-metric-processor.sh

.if-dev-mr-pipeline: &if-dev-mr-pipeline
  if: "$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $DEV_BRANCH"

.infrastructure:base:
  image: $REGISTRY/terragrunt:latest
  variables:
    OPTIONS: "-concise"
    SERVICE_ACCOUNT_KEY: ${TARGET_LANDING_ZONE}-cicd-sa.json
  before_script:
    - cd ${CI_PROJECT_DIR}/environments
    - !reference [.gcp-auth-base, before_script]
  script:
    - *mise-build
    - ${CI_PROJECT_DIR}/scripts/terragrunt.sh $ACTION --environment $TARGET_ENVIRONMENT --options $OPTIONS

# =========================================
# Dev, Stage, Prod Environments
# =========================================

infrastructure:check:
  stage: validate
  variables:
    SERVICE_ACCOUNT_KEY: ${TARGET_LANDING_ZONE}-cicd-sa.json
  before_script:
    - cd ${CI_PROJECT_DIR}/environments
    - !reference [.gcp-auth-base, before_script]
  script:
    - terragrunt hcl validate --inputs --all
    - terragrunt hcl fmt --check --all --exclude-dir=.terragrunt-cache
  rules:
    - if: "$CI_MERGE_REQUEST_TARGET_BRANCH_PROTECTED == 'true'"

infrastructure:docker-build:
  stage: build
  services:
    - name: $BUILD_IMAGE
  variables:
    SERVICE_ACCOUNT_KEY: ops-cicd-sa.json
  before_script:
    - !reference [.gcp-auth-base, before_script]
    - gcloud auth configure-docker us-docker.pkg.dev --quiet
  script:
    - *build-common-docker-images
  artifacts:
    untracked: true
    paths:
      - /tmp/**/build.log

infrastructure:plan:
  stage: build
  needs:
    - infrastructure:check
    - infrastructure:docker-build
  extends:
    - .infrastructure:base
  variables:
    ACTION: plan
  parallel:
    matrix:
      - TARGET_ENVIRONMENT: dev
        TARGET_LANDING_ZONE: nonprod
        MR_TARGET: dev
      - TARGET_ENVIRONMENT: stage
        TARGET_LANDING_ZONE: nonprod
        MR_TARGET: dev
      - TARGET_ENVIRONMENT: prod
        TARGET_LANDING_ZONE: prod
        MR_TARGET: dev
      - TARGET_ENVIRONMENT: stage
        TARGET_LANDING_ZONE: nonprod
        MR_TARGET: stage
      - TARGET_ENVIRONMENT: prod
        TARGET_LANDING_ZONE: prod
        MR_TARGET: stage
      - TARGET_ENVIRONMENT: prod
        TARGET_LANDING_ZONE: prod
        MR_TARGET: main
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $MR_TARGET'

infrastructure:deploy:
  stage: deploy
  needs:
    - infrastructure:docker-build
  extends:
    - .infrastructure:base
  variables:
    ACTION: apply
    OPTIONS: "-auto-approve"
  rules:
    - if: "$CI_COMMIT_REF_PROTECTED == 'true'"
