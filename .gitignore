# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.nx

*.zip

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc/istanbul test coverage
.nyc_output

# SonarQube
.scannerwork/

# Editor directories and files
.idea
*.iml
.vscode/**

# Dependency directories
node_modules/
jspm_packages/
package-lock.json
libs/api/ict-api-foundations/yarn.lock
libs/api/ict-api-foundations/package-lock.json

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Webpack
.webpack

# Build / Compile directories
build/
dist/
ts-built/

# dotenv environment variables file
.env.test
.env.servicePrincipal
.env.yaml
.npmrc
.env.yaml
.env.local
.env.*.local

# Serverless directories
.serverless/
.serverless_plugins/

# Azure Functions artifacts
bin
obj
appsettings.json

#local secrets
localsecrets
*.secrets.yml

#previous config files
*.old

# Operating system files
.DS_Store

gitkey
iothubcerts
deploy-azure
function.json

azure-functions-core-tools

integration.secrets.js

# local Blob storage files
localBlobStorage
/runDev.sh
az-storage
dump

# ignore generated files by scripts
*.generated.*

# Azurite files
__azurite*.json

# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*
*.terraform.lock.hcl

# yarn, https://yarnpkg.com/getting-started/qa#which-files-should-be-gitignored
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# nx - local nx dev cache
.nx

# Test Output
test-results/

# SDK Generated Code
libs/shared/ict-sdk-foundations/sdk-foundations/**/*.*
libs/shared/ict-sdk-foundations/sdk-foundations/defs**

# We merge all OpenAPI docs, do not commit microservice versions
apps/**/docs/openapi.yaml
cloudrun/**/docs/openapi.yaml

# Docs-for-backstage
site/

# Pre-monorepo generated files directory
foundations/ict-sdk-foundations/sdk-foundations/
node_modules

# Python
.venv
__pycache__/
.pytest_cache/

# VSCode workspace files
*.code-workspace

# MkDocs build script
/MkDocs-build-docs-clean-run-from-root.ps1