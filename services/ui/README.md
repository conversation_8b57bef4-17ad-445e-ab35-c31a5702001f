# ICT UI Carbon

## Description

ICT UI is a React-based application leveraging the IBM Carbon Design System

## Features

- **React 19**: Built with the latest React version for performance and maintainability.
- **IBM Carbon Design System**: Ensures a consistent and accessible UI.
- **Single-SPA Integration**: Supports microfrontend architecture.
- **React Query & OpenAPI Integration**: Efficient data fetching and caching.

## Requirements

- **Node.js >= 20.0.0**

## Installation

```bash
# Clone the repository
git clone ...
cd ...

# Install dependencies
yarn install
```

## Development

```bash
# Start the development server
yarn start
```

## Build

```bash
# Development Build
yarn build:dev

# Staging Build
yarn build:stage

# Production Build
yarn build:prod
```

## Testing & Linting

```bash
# Run tests
yarn test

# Run tests with coverage
yarn test:coverage

# Lint code
yarn lint
```

## Preview Build

```bash
yarn preview
```
