{"Active Operators": "Opérateurs actifs", "Add View": "Ajouter une vue", "addViewModal": {"nameLabel": "Nom de la vue", "namePlaceholder": "Entrez le nom de la vue", "nameRequired": "Le nom de la vue est obligatoire", "nameTaken": "Ce nom de vue est déjà utilisé", "typeLabel": "Type de vue"}, "Advice Cycle Time": "Durée du cycle de conseil", "adviceDetailsDrawer": {"adviceLine": "Ligne d'assistance", "closeButton": "<PERSON><PERSON><PERSON>", "errorLoading": "Erreur lors du chargement des détails du conseil", "handlingUnit": "<PERSON><PERSON>", "handlingUnitType": "Type d'unité de manutention", "itemsReceivedHeading": "Articles reçus", "linesLabel": "Lignes:", "loading": "Chargement des détails du conseil...", "noItemsReceived": "Aucun article reçu", "noValue": "--", "packagingLevel": "Niveau d'emballage", "quantity": "Quantité", "sku": "UGS", "statusLabel": "Statut:", "supplierIdLabel": "ID du fournisseur :", "typeLabel": "Taper:"}, "Are you sure you want to delete this menu item?": "Êtes-vous sûr de vouloir supprimer cet élément de menu ?", "authErrorPage": {"contactAdministrator": "Contacter l'administrateur", "logout": "Se déconnecter", "noOrganisations": "Aucune organisation", "noOrganisationsDescription": "Votre compte n'appartient à aucune organisation pour le moment. Veuillez contacter votre administrateur pour l'ajouter.", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknownError": "<PERSON><PERSON><PERSON> inconnue", "unknownErrorDescription": "Une erreur inconnue s'est produite. Veuillez contacter votre administrateur.", "unverifiedEmail": "E-mail non vérifié", "unverifiedEmailDescription": "Veuillez vérifier votre adresse e-mail pour continuer. Consultez votre boîte de réception pour obtenir un lien de vérification.", "unverifiedEmailResend": "Renvoyer l'e-mail de vérification"}, "barChartWidgetOptions": {"barColor": "<PERSON><PERSON><PERSON> de la <PERSON>re", "chartTitle": "Titre du graphique", "chartType": "Type de graphique", "dataTitle": "<PERSON><PERSON><PERSON>", "displayTitle": "<PERSON><PERSON><PERSON><PERSON>", "groupBy": "Grouper par", "orientation": "Orientation", "orientationHorizontal": "Horizontal (barres)", "orientationVertical": "Vertical (colonnes)", "showAverageLine": "Afficher la ligne moyenne", "showTargetLine": "Afficher la ligne cible", "sortAscAZ": "Croissant (A à Z)", "sortAscLowHigh": "Ascendant (du plus bas au plus haut)", "sortByName": "Trier par nom", "sortByValue": "Trier par valeur", "sortDescHighLow": "Descendant (de haut en bas)", "sortDescZA": "Décroissant (Z à A)", "sortingTitle": "Tri", "sortNone": "Aucun", "targetValue": "Valeur cible"}, "Cancel": "Annuler", "Changed By": "Modifié par", "chartComponent": {"noChartData": "Aucune donnée graphique disponible"}, "chartWidgetOptions": {"activeTime": "Temps actif", "blockedTime": "<PERSON><PERSON> bloq<PERSON>", "chartStyle": "Style de graphique", "chartTitle": "Titre du graphique", "chartType": "Type de graphique", "dataTitle": "<PERSON><PERSON><PERSON>", "displayTitle": "<PERSON><PERSON><PERSON><PERSON>", "donorTotesPerHour": "Nombre de sacs de donateurs par heure", "idleTime": "Temps d'inactivité", "linesPerHour": "Lignes/heure", "operatorId": "ID de l'opérateur", "orderTotesPerHour": "Commandes de bacs/heure", "quantityPerHour": "Quantité/Heure", "showAverageLine": "Afficher la ligne moyenne", "showTargetLine": "Afficher la ligne cible", "starvedTime": "Temps affamé", "status": "Statut", "styleArea": "Zone", "styleColumn": "Colonne", "styleLine": "Doubler", "styleStackedArea": "Zone empilée", "styleStackedColumn": "Colonne empi<PERSON>", "targetValue": "Valeur cible", "weightedLinesPerHour": "<PERSON>gnes pond<PERSON>/heure", "weightedQuantityPerHour": "Quantité pondérée/heure", "workflowStatus": "Statut du flux de travail", "workMode": "Mode de travail", "workstation": "Poste de travail", "workstationListTitle": "Liste des postes de travail"}, "Collapse All": "<PERSON><PERSON><PERSON><PERSON> tout", "configErrorBoundary": {"localhostOverrideError": "Il semble que vous ayez défini l'API de configuration sur localhost, ce qui peut être à l'origine de cette erreur.", "logout": "Se déconnecter", "refreshPage": "Actualiser la page", "resetApiOverrides": "Réinitialiser les remplacements d'API", "title": "<PERSON><PERSON><PERSON> chose s'est mal passé", "unexpectedError": "Une erreur inattendue s'est produite lors du chargement de cette page."}, "containerDetail": {"avgDailyCycleCount": "Nombre moyen de cycles quotidiens", "avgDailyPickEvents": "Événements quotidiens moyens", "containerList": "Liste des conteneurs", "cycleCountEventsToday": "Événements du Cycle Count aujourd'hui", "pickEventsToday": "Choisissez des événements aujourd'hui"}, "containerList": {"lastUpdated": "Dernière mise à jour {{formattedLastUpdated}}", "loading": "Chargement..."}, "containerListTable": {"containerId": "ID du conteneur", "dataUpdated": "Donn<PERSON> mises à jour", "freeCycleCount": "Comptage de cycles gratuit", "lastActivity": "Dernière activité", "lastCycleCount": "Dernier décompte des cycles", "locationId": "ID de localisation", "notAvailable": "N / A", "quantity": "Quantité", "sku": "UGS", "zone": "Zone"}, "configuredAlerts": {"title": "Alertes configurées", "subject": "Sujet", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view": "Voir", "recipients": "<PERSON><PERSON><PERSON><PERSON>", "visibility": "Visibilité", "frequency": "E-mail envoyé (au maximum)", "authenticating": "Authentification...", "authError": "Erreur d'authentification", "notAuthenticated": "Non authentifié", "notAuthenticatedMessage": "Vous devez vous authentifier pour voir les alertes Tableau.", "viewAll": "Voir tout"}, "curatedDataTable": {"errorLoadingData": "<PERSON><PERSON><PERSON> donnée disponible"}, "Customer Cycle Time": "Temps de cycle client", "Customer Line Progress": "Progression de la ligne client", "Customer Line Throughput Rate": "Taux de rendement de la ligne client", "Customer Order Progress": "Progression de la commande client", "Customer Orders Shipped": "Commandes clients expédiées", "Cycle Counts": "Comptages cycliques", "dailyPerformanceTable": {"asrsRetrieval": "Récupération ASRS", "asrsStorage": "Stockage ASRS", "date": "Date", "dmsRetrieval": "Récupération DMS", "dmsStorage": "Stockage DMS", "donorContainers": "Conteneurs de donateurs", "firstShiftPercent": "1er quart de travail %", "gtpContainers": "Conteneurs GTP", "idlePercent": "Inactif %", "linesPerHour": "Lignes/heure", "linesPicked": "Lignes choisies", "pickLineQty": "Quantité de ligne de prélèvement", "qtyPerLine": "Qté/Ligne", "secondShiftPercent": "2e quart de travail %", "starvedHours": "Heures affamées", "starvedPercent": "Affamé %", "totalHours": "Nombre total d'heures"}, "datagrid": {"ascending": "Ascendant", "descending": "Descendant", "errorLoadingData": "Erreur lors du chargement des données : {{error}}", "loadingData": "Chargement des données...", "noDataAvailable": "<PERSON><PERSON><PERSON> donnée disponible"}, "datagridHeader": {"export": "Exporter", "exportOptions": "Options d'exportation", "searchTable": "Tableau de recherche"}, "Default Route": "Route par défaut", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Delete item": "Supprimer l'élément", "Delete Menu Item": "Supprimer l'élément de menu", "dmsLevelView": {"binLocation": "Emplacement du bac", "containerType": "Type de conteneur", "heading": "Emplacement du bac", "skuAndQuantity": "SKU et quantité", "status": "Statut"}, "Edit item": "Modifier l'élément", "emailVerification": {"authenticationError": "Erreur d'authentification", "authErrorMessage": "Erreur d'authentification. Veuillez contacter votre administrateur.", "emailAlreadyVerified": "E-mail déjà vérifié", "emailSent": "E-mail envoyé", "emailSentSuccessfully": "La vérification de l'e-mail a été renvoyée avec succès", "error": "<PERSON><PERSON><PERSON>", "genericError": "Échec du renvoi de l'e-mail de vérification. Veuillez réessayer ou contacter l'assistance.", "rateLimitExceeded": "Limite de débit dépassée", "rateLimitMessage": "Trop de demandes de vérification par e-mail. Veuillez réessayer dans 24 heures.", "userNotFound": "Utilisateur non trouvé", "userNotFoundMessage": "Utilisateur introuvable. Veuillez contacter votre administrateur."}, "Enable All": "Activer tout", "Error loading menu configuration": "Erreur lors du chargement de la configuration du menu", "Estimated Completion": "Achèvement estimé", "Expand All": "<PERSON><PERSON> d<PERSON>vel<PERSON>per", "exportDialog": {"unknownError": "Une erreur inconnue s'est produite lors de l'exportation."}, "Facility Estimated Completion": "Achèvement estimé des installations", "Facility Order Progress": "Progression des commandes d'installations", "Facility Orders Shipped": "Commandes expédiées", "facilityGuard": {"errorTitle": "Erreur d'accès aux installations", "loadingFacilities": "Installations de chargement...", "logout": "Se déconnecter", "noFacilitiesError": "Vous n'avez accès à aucune fonctionnalité. Veuillez contacter votre administrateur.", "settingUpFacility": "Mise en place de l'accès aux installations..."}, "facilityProcessFlowDetailPanel": {"noMetricsAvailable": "Aucune mesure disponible pour cet élément"}, "fileUpload": {"chooseDocuments": "Choisissez les documents à télécharger", "date": "Date", "deleteFileDescription": "<PERSON><PERSON><PERSON><PERSON> le fichier", "fileSelected": "<PERSON><PERSON><PERSON>", "fileUploadHistory": "Historique de téléchargement de fichiers", "knownOrderCount": "Nombre de lignes de commande connu", "knownOrderLineCount": "Nombre de lignes de commande connu", "replaceFile": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>", "selectFile": "Sé<PERSON><PERSON><PERSON> le fichier", "step1": "Étape 1 : Fichier du gestionnaire de commandes SAP", "step2": "Étape 2 : <PERSON><PERSON><PERSON> de détails de commande SAP", "supportedFileFormats": "Formats de fichiers pris en charge : .xlsx, .xls", "uploadFailed": "Échec du téléchargement", "uploadFilesButton": "Télécharger des fichiers", "uploading": "Téléchargement en cours...", "uploadSuccessful": "Téléchargement réussi", "viewBarTitle": "Téléchargement de fichiers"}, "Finished Advices": "Conseils terminés", "formatAvg": {"defaultUnitString": "Moy. : {{avg}} {{unit}}", "noUnit": "Moyenne : {{avg}}", "percentage": "Moyenne : {{avg}} %"}, "formatTarget": {"defaultUnitString": "Cible : {{target}} {{unit}}", "noUnit": "Cible : {{target}}", "percentage": "Cible : {{target}} %"}, "formatTime": {"hasHours": "{{hours}} heures {{minutes}} minutes", "hasHoursNoMinutes": "{{hours}} heures", "hasMinutes": "{{minutes}} min {{seconds}} secondes", "hasMinutesNoSeconds": "{{minutes}} min", "hasSeconds": "{{seconds}} secondes", "nan": "0 {{unit}}"}, "formatTotal": {"defaultUnitString": "{{totalFormatted}} {{defaultUnitString}}", "percentage": "{{totalFormatted}} %"}, "Go Home": "Rentrer à la maison", "In Progress Advices": "Conseils en cours", "Inventory Accuracy": "Exactitude de l'inventaire", "inventoryForecastTimestamp": {"inventoryDataUpdated": "Données d'inventaire mises à jour : {{dataUpdateTimestamp}}", "loadingTimestampData": "Chargement des données d'horodatage...", "noDataAvailable": "<PERSON><PERSON><PERSON> donnée disponible", "skuForecastPerformed": "Prévision SKU effectuée : {{analysisPerformedTimestamp}}"}, "inventoryList": {"lastUpdated": "Dernière mise à jour : {{formattedLastUpdated}}", "loading": "Chargement..."}, "inventoryListTable": {"avgDailyOrders": "Commandes quotidiennes moyennes", "avgDailyQty": "Quantité quotidienne moyenne", "contOverage": "Excédent", "daysOnHand": "Jours disponibles", "description": "Description", "latestActivity": "Dernière activité", "latestCycleCount": "Dernier décompte des cycles", "maxContainers": "Conteneurs Max", "qtyAllocated": "Quantité allouée", "qtyAvailable": "Quantité disponible", "sku": "UGS", "skuPositions": "Positions des UGS", "targetMultiplicity": "Multiplicité des cibles", "velocityClassification": "Classification de la vitesse"}, "itemDetails": {"description": "Ceci est une vue détaillée de l'élément {{itemId}}", "heading": "Détails de l'article {{itemId}}", "item1": "Article 1", "routableView": "Vue routable", "title": "Détails de l'article"}, "itemList": {"heading": "Liste des articles", "routableExampleView": "Exemple de vue routable", "viewItem1": "Voir l'article 1"}, "kpiChartWidgetOptions": {"chartStyle": "Style de graphique", "chartTitle": "Titre du graphique", "chartType": "Type de graphique", "color": "<PERSON><PERSON><PERSON>", "dataTitle": "<PERSON><PERSON><PERSON>", "displayId": "afficher", "displayTitle": "<PERSON><PERSON><PERSON><PERSON>", "kpiValueLabel": "Étiquette de valeur KPI", "kpiValuePlaceholder": "Étiquette affichée sous la valeur KPI", "precisionLabel": "Précision", "showAverageKpi": "Afficher les KPI moyens", "showAverageLine": "Afficher la ligne moyenne", "showCurrentKpi": "Afficher les KPI actuels", "showMaxKpi": "Afficher le KPI maximal", "showMinKpi": "Afficher le KPI minimal", "showTargetLine": "Afficher la ligne cible", "showTotalKpi": "Afficher le total des KPI", "showUnit": "Afficher l'unité", "styleArea": "Zone", "styleColumn": "Colonne", "styleLine": "Doubler", "stylingTitle": "Style", "targetValue": "Valeur cible"}, "Last 14 Days": "Les 14 derniers jours", "Last 30 Days": "Les 30 derniers jours", "Last 7 Days": "Les 7 derniers jours", "Last Month": "<PERSON><PERSON>", "Last Week": "La semaine dernière", "last30Days": "Les 30 derniers jours", "last60Days": "Les 60 derniers jours", "last7Days": "Les 7 derniers jours", "Level": "Niveau", "Loading": {"": "Chargement..."}, "loggedInWidgetOperators": {"actualLabel": "<PERSON><PERSON><PERSON>", "expectedLabel": "<PERSON><PERSON><PERSON>", "title": "Opérateurs connectés", "totalLinesPerHour": "Nombre total de lignes/h"}, "menu": {"administration": "Administration", "advancedOrchestration": "Orchestration avancée", "aiChat": "Chat IA", "alarms": "Alarmes", "applicationConfig": "Configuration de l'application", "applicationHealth": "Santé des applications", "automationOverview": "Présentation de l'automatisation", "automationVisualization": "Visualisation de l'automatisation", "configuredAlerts": "Alertes configurées", "containerList": "Liste des conteneurs", "controls": "<PERSON><PERSON><PERSON><PERSON>", "curatedData": "Données organisées", "dailyPerformanceReport": "Rapport de performance quotidien", "dashboardManager": "Gestionnaire de tableau de bord", "dataExplorer": "Explorateur de données", "debugInfo": "Informations de débogage", "dematicChat": "<PERSON><PERSON>", "dematicInternal": "Dématique interne", "dematicSearch": "Recherche Dematic", "devices": "Appareils", "equipment": "Équipement", "equipmentHierarchy": "Hiérarchie des équipements", "events": "Événements", "examples": "Exemples", "facilityOverview": "Aperçu des installations", "facilityProcessFlow": "Flux de processus de l'installation", "facilityProcessFlowDataStalenessIndicator": {"pollingErrorTitle": "Échec de l'actualisation des données", "pollingErrorMessage": "La dernière actualisation des données a échoué. Affichage des données les plus récentes disponibles."}, "faultTracking": "Suivi des défauts", "featureFlags": "Drapeaux de fonctionnalités", "fileUpload": "Téléchargement de fichiers", "functionalAreas": "Domaines fonctionnels", "groups": "Groupes", "inboundOverview": "Aperçu des entrées", "inventory": "Inventaire", "inventoryForecast": "Prévisions d'inventaire", "inventoryList": "Liste d'inventaire", "inventoryOverview": "Aperçu de l'inventaire", "kpis": "indicateurs clés de performance", "locations": "Emplacements", "menuManager": "Gestionnaire de menus", "mfeManager": "Responsable MFE", "myDashboards": "Mes tableaux de bord", "operationalAlerting": "Alerte opérationnelle", "operationsVisibility": "Visibilité des opérations", "optionsView": "Options d'affichage", "outboundOverview": "Aperçu des sorties", "performanceAnalysis": "Analyse des performances", "permissions": "Autorisations", "pickingBufferAreaDetails": "Détails de la zone tampon de prélèvement", "playground": "Aire de jeux", "processFlowVisualization": "Visualisation du flux de processus", "replenishmentDetails": "Détails du réapprovisionnement", "routableView": "Vue routable", "rules": "<PERSON><PERSON><PERSON>", "scenarioModeling": "Modélisation de scénarios", "simulation": "Simulation", "staticView": "Vue statique", "systemAvailability": "Disponibilité du système", "systemHealth": "<PERSON><PERSON> du système", "users": "Utilisateurs", "workstation": "Poste de travail", "workstationActiveOrders": "Commandes Actives", "workstationOverview": "Présentation du poste de travail"}, "Menu configuration has been saved successfully": "La configuration du menu a été enregistrée avec succès", "Menu configuration not found": "Configuration du menu non trouvée", "Menu Manager": "Gestionnaire de menus", "Menu Manager Tree": "Arborescence du gestionnaire de menus", "Menu Name": "Nom du menu", "menuManager": {"failedToSaveConfiguration": "Échec de l'enregistrement de la configuration du menu : {{error}}"}, "New Value": "Nouvelle valeur", "No menu items found": "Aucun élément de menu trouvé", "No menu setting found in the configuration": "Aucun paramètre de menu trouvé dans la configuration", "notFound": {"message": "La page que vous recherchez n'existe pas ou a peut-être été déplacée.", "title": "Page non trouvée"}, "Old Value": "Ancienne valeur", "Order Cycle Time": "Temps de cycle de commande", "Order Lines Progress": "Progression des lignes de commande", "Orders Outstanding": "Commandes en cours", "Outstanding Advices": "Conseils exceptionnels", "Overall Outbound Rate": "Taux global de sortie", "pdfGenerator": {"analysis": "Analyse", "caseQuantity": "Quantité de caisses", "casesRequired": "Cas requis", "conditionCode": "Code de condition", "containers": "Conteneurs", "createdDate": "pdfGenerator.createdDate", "data": "<PERSON><PERSON><PERSON>", "eaches": "{{quantity}} pièces", "forecastLabel": "Prévisions pour demain", "locationId": "ID de localisation", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "noReserveLocationsError": "Aucun emplacement trouvé sous « Stockage de réserve » pour les références sélectionnées. Impossible de générer le PDF.", "pageOfTotal": "pdfGenerator.pageOfTotal", "performed": "pdfGenerator.performed", "skuOfTotal": "pdfGenerator.skuOfTotal", "skuQuantity": "Quantité SKU", "skus": "UGS", "taskDescription": "Créez de nouvelles tâches de génération de déplacement pour déplacer les SKU inclus des emplacements de stockage de réserve vers les ASRS de prélèvement direct.", "taskLabel": "Tâche : G<PERSON><PERSON>rer un déplacement vers ASRS", "updated": "pdfGenerator.mis à jour"}, "pieChartWidgetOptions": {"chartTitle": "Titre du graphique", "chartType": "Type de graphique", "dataTitle": "<PERSON><PERSON><PERSON>", "displayTitle": "<PERSON><PERSON><PERSON><PERSON>", "groupBy": "Grouper par", "pieType": "Type de tarte", "pieTypeDonut": "Donut", "pieTypePie": "Tarte", "showPercentage": "A<PERSON><PERSON><PERSON> le pourcentage"}, "progress": {"format": "{{current}} sur {{total}}"}, "Projected Order Fulfillment": "Exécution des commandes projetées", "Property Updated": "Propri<PERSON><PERSON> mise à jour", "recentSearchesView": {"lastSevenDays": "Les 7 derniers jours", "lastThirtyDays": "Les 30 derniers jours", "today": "<PERSON><PERSON><PERSON>'hui", "view": "Voir", "zeroRecentSearches": "Vous avez 0 recherches récentes."}, "searchResultsView": {"noResults": "Aucun résultat"}, "Select workstations": "Sélectionner les postes de travail", "siteTimeText": {"siteTime": "{{value, formatDate}}"}, "stationHealthWidget": {"title": "Santé de la station", "totalDownTime": "Temps d'arrêt total"}, "stationPerformanceWidget": {"title": "Performance de la station", "totalActiveTime": "Temps total d'activité", "totalStarvedTime": "Temps total de famine"}, "Storage Utilization": "Utilisation du stockage", "tenantFacilityDisplay": {"facility": "Facilité"}, "This Month": "Ce mois-ci", "This Week": "<PERSON><PERSON> se<PERSON>", "Throughput Rate": "Débit", "Time Changed": "Le temps a changé", "Today": "<PERSON><PERSON><PERSON>'hui", "totalStationsWidgetProps": {"activeCount": "Actif : {{number}}", "inactiveCount": "Inactif : {{number}}", "modeHeading": "Mode", "statusHeading": "Statut", "title": "Stations totales"}, "Units per Hour": "Unités par heure", "Units Remaining": "Unités restantes", "viewHistory": {"apply": "Appliquer", "changedBy": "Modifié par", "defaultValue": "Valeur par défaut", "defaultValueDescription": "La valeur par défaut pour la vue", "noHistoryAvailable": "Aucun historique disponible", "noHistoryEntriesFound": "Aucune entrée d'historique trouvée.", "searchHistory": "Rechercher dans l'historique", "title": "Historique de la vue", "viewHistoryEntries": "Voir les entrées d'historique"}, "Unsaved Changes": "Modifications non enregistrées", "widget": {"delete": "<PERSON><PERSON><PERSON><PERSON>", "loadingOptions": "Options de chargement", "loadingWidget": "Chargement du widget", "settings": "Paramètres"}, "widgetContainer": {"errorTitle": "<PERSON><PERSON><PERSON>", "genericErrorOccurred": "Une erreur s'est produite.", "initializingSubtitle": "Cliquez sur les trois points pour configurer le widget.", "initializingTitle": "Commencer", "loadingData": "Chargement des données", "noDataSubtitle": "Aucune donnée disponible pour la période actuelle. Veuillez consulter à nouveau cette page ultérieurement.", "noDataTitle": "<PERSON><PERSON><PERSON> donnée disponible"}, "workstation": {"orderStatusWidget": {"activeOrder": {"singular": "1 commande active"}, "activeOrders": {"plural": "{{amount}} commandes actives"}, "notice": {"subtitle": {"plural": "{{amount}} commandes actives sont ouvertes depuis plus de 45 minutes", "singular": "1 commande active est ouverte depuis plus de 45 minutes"}, "title": "<PERSON><PERSON>"}, "status": {"delayed": "{{amount}} Retardé", "healthy": "En bonne santé"}, "title": "Statut de la commande"}}, "workstationOrderDetail": {"deliveryNumber": "Num<PERSON><PERSON> de liv<PERSON>", "orderStatus": "Statut de la commande", "pickLines": "Lignes de prélèvement", "workstationOverview": "Présentation du poste de travail"}, "workstationOrderPicks": {"container": "Récipient", "containerHeader": "Récipient", "containers": "Conteneurs", "eventTimeHeader": "Heure de l'événement", "lastLocationHeader": "Dernier emplacement", "loading": "Chargement...", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "orderLine": "Ligne de commande", "qtyOrdered": "Quantité commandée", "qtyPicked": "Quantité prélevée", "quantityHeader": "Quantité", "size": "<PERSON><PERSON>", "status": "Statut", "statusHeader": "Statut", "style": "Style", "transportHeader": "Transport"}, "workstationOrderStatus": {"activeOrders": "Commandes actives", "arrivalTime": "<PERSON>ure d'arrivée", "completedPicks": "<PERSON><PERSON> term<PERSON>", "container": "Récipient", "dwellTime": "Temps de séjour", "orderId": "ID de commande", "orderStatus": "Statut de la commande", "pickTask": "Choisir une tâche", "position": "Position", "station": "Gare", "title": "Statut de la commande", "workstationOverview": "Présentation du poste de travail"}, "Yesterday": "<PERSON>er"}