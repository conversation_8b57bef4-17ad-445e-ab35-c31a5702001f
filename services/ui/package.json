{"name": "ict-ui-carbon", "description": "ICT UI utilizing React and IBM Carbon Design System", "version": "0.1.0", "type": "module", "engineStrict": true, "engines": {"node": ">=22.0.0"}, "scripts": {"start": "vite --mode=localhost", "build": "yarn run build:dev", "build:dev": "vite build --mode development", "build:stage": "vite build --mode staging", "build:prod": "vite build --mode production", "test": "vitest", "test:debug": "vitest --ui --watch", "test:coverage": "vitest --coverage", "typecheck": "tsc --build --noEmit", "lint": "eslint --fix", "preview": "vite preview", "extract": "yarn run i18next"}, "dependencies": {"@auth0/auth0-react": "^2.3.0", "@bufbuild/protobuf": "^2.5.1", "@carbon/charts-react": "^1.23.10", "@carbon/icons-react": "^11.60.0", "@carbon/react": "^1.83.0", "@dagrejs/dagre": "^1.1.4", "@tanstack/react-query": "^5.79.0", "@tanstack/react-table": "^8.21.3", "@uidotdev/usehooks": "^2.4.1", "axios": "^1.9.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "i18next-parser": "^9.3.0", "jspdf": "^3.0.1", "luxon": "^3.6.1", "openapi-fetch": "^0.14.0", "openapi-react-query": "^0.5.0", "react": "19.1.0", "react-complex-tree": "^2.6.0", "react-dom": "19.1.0", "react-grid-layout": "^1.5.1", "react-i18next": "^15.5.2", "react-icons": "^5.5.0", "react-router": "^7.6.1", "reactflow": "^11.11.4", "single-spa": "^6.0.3", "single-spa-react": "^6.0.2", "xlsx": "^0.18.5", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "9.29.0", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/luxon": "3.6.2", "@types/node": "22.15.31", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/react-grid-layout": "1.3.5", "@vitejs/plugin-react": "4.5.2", "@vitest/coverage-v8": "3.2.0", "@vitest/ui": "3.2.0", "babel-plugin-react-compiler": "19.0.0-beta-ebf51a3-20250411", "eslint": "9.29.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-react": "7.37.5", "eslint-plugin-unicorn": "59.0.1", "globals": "16.2.0", "i18next-scanner": "4.6.0", "jsdom": "26.1.0", "prettier": "3.5.3", "react-is": "19.1.0", "sass": "1.89.2", "typescript": "5.8.3", "typescript-eslint": "8.34.0", "vite": "6.3.5", "vite-plugin-single-spa": "1.0.0", "vitest": "3.2.0"}, "packageManager": "yarn@4.7.0"}