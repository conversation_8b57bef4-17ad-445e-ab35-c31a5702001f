import { Auth0Provider } from "@auth0/auth0-react";
import { ReactNode, useEffect } from "react";
import { getTableauAuthConfig } from "../../config/auth/auth.config";
import { TableauAuthContext } from "./tableau-auth-context";
import { useAuth0 } from "@auth0/auth0-react";
import { tableauAuthService } from "./tableau-auth-service";
import { useRoles } from "../hooks/use-roles";
import { SecurityRoles } from "@ict/sdk/types";

interface TableauAuthProviderProps {
  children: ReactNode;
}

/**
 * Internal component that handles Tableau auth initialization after Auth0 is set up
 */
function TableauAuthInitializer({ children }: { children: ReactNode }) {
  const auth0Client = useAuth0(TableauAuthContext);

  useEffect(() => {
    tableauAuthService.setAuth0Client(auth0Client);
  }, [auth0Client]);

  return children;
}

/**
 * Provider component that sets up Auth0 for Tableau authentication and initializes the Tableau auth service.
 */
export function TableauAuthProvider({ children }: TableauAuthProviderProps) {
  const config = getTableauAuthConfig();
  const { actualRoles } = useRoles();

  const roles = [
    SecurityRoles.CT_TABLEAU_ADMIN,
    SecurityRoles.CT_TABLEAU_EXPLORER,
    SecurityRoles.CT_TABLEAU_VIEWER,
  ];

  // If the user has any of the tableau roles, show the tableau auth provider
  const isTableauUser = actualRoles.some((role) =>
    roles.includes(role as SecurityRoles),
  );

  if (!isTableauUser) {
    return children;
  }
  return (
    <Auth0Provider
      context={TableauAuthContext}
      domain={config.domain}
      clientId={config.clientId}
      authorizationParams={{
        audience: config.audience,
        scope: "tableau:content:read tableau:data_driven_alerts:read",
        redirect_uri: window.location.origin,
      }}
    >
      <TableauAuthInitializer>{children}</TableauAuthInitializer>
    </Auth0Provider>
  );
}
