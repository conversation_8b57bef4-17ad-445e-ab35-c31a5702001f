import { useEffect, useState } from "react";
import { useNotification } from "../components/toast/use-notification";
import type { AppConfigSetting } from "../config/hooks/use-config";
import { updateConfigSetting } from "../config/hooks/use-config";
import { useOutletContext } from "react-router";
import { ViewHistory } from "../views/components/view-history/view-history";
import { AppConfigSettingSource } from "@ict/sdk/types";
import { useTranslation } from "react-i18next";

export interface OptionsComponentProps<T> {
  options: T;
  onClose: () => void;
  onSave: () => void;
  onChange: (updatedOptions: T) => void;
}

export interface UseViewOptionsProps<T> {
  /** The configuration setting from the backend */
  setting: AppConfigSetting;

  /** Default options to use if no setting value exists */
  defaultOptions?: T;

  /** Component to render the options panel */
  optionsComponent: React.ComponentType<OptionsComponentProps<T>>;

  /** Message to show when settings are successfully saved */
  successMessage?: string;

  /** Message to show when settings are not successfully saved */
  errorMessage?: string;
}

/**
 * A hook for managing view options/settings with an aside panel UI
 *
 * This hook encapsulates the common pattern of:
 * - Showing a settings panel in an aside
 * - Managing draft changes that can be saved or cancelled
 * - Persisting settings to the backend
 * - Showing success/error notifications
 *
 * It's designed to be reused across different views that need configuration options,
 * reducing duplicate code and ensuring consistent behavior.
 *
 * @template T - The type of options being managed, must be a record/object
 * @param {UseViewOptionsProps<T>} props - Configuration for the hook
 * @returns Various state values and handlers for managing view options
 */
export function useViewOptions<T extends Record<string, unknown>>({
  setting,
  defaultOptions = {} as T,
  optionsComponent,
  successMessage,
  errorMessage,
}: UseViewOptionsProps<T>) {
  const { t } = useTranslation();
  const defaultSuccessMessage = t(
    "useViewOptions.defaultSuccessMessaage",
    "Your settings have been saved.",
  );
  const defaultErrorMessage = t(
    "useViewOptions.defaultErrorMessage",
    "Your settings were not saved.",
  );

  const handleShowViewAside =
    useOutletContext<(content: React.ReactNode) => void>();

  const { success, error } = useNotification();

  // Keep the original setting in state
  const [localSetting, setLocalSetting] = useState<AppConfigSetting>(setting);

  // Keep a draft of changes that can be reverted
  const [draftOptions, setDraftOptions] = useState<T>(
    (setting?.value as T) || defaultOptions,
  );

  const [isDirty, setIsDirty] = useState(false);
  const [isSettingsPanelOpen, setIsSettingsPanelOpen] = useState(false);
  const [isHistoryPanelOpen, setIsHistoryPanelOpen] = useState(false);

  // Function to check if the draft options differ from the original options
  const hasChanges = (current: T, original: T): boolean => {
    return JSON.stringify(current) !== JSON.stringify(original);
  };

  // Update local state when props change
  useEffect(() => {
    setLocalSetting(setting);
    setDraftOptions((setting?.value as T) || defaultOptions);
  }, [setting]);

  // Effect to update isDirty when draftOptions change
  useEffect(() => {
    const originalOptions = (localSetting?.value as T) || defaultOptions;
    setIsDirty(hasChanges(draftOptions, originalOptions));
  }, [draftOptions, localSetting]);

  // Effect to update the aside content whenever options change
  useEffect(() => {
    if (isSettingsPanelOpen) {
      renderSettingsPanel();
    }
  }, [isSettingsPanelOpen, draftOptions]);

  // Clean up when component unmounts
  useEffect(() => {
    return () => {
      if (isSettingsPanelOpen) {
        setIsSettingsPanelOpen(false);
        handleShowViewAside(null);
      }
      if (isHistoryPanelOpen) {
        setIsHistoryPanelOpen(false);
        handleShowViewAside(null);
      }
    };
  }, [isSettingsPanelOpen, isHistoryPanelOpen, handleShowViewAside]);

  const renderOptionsPanel = (
    options: T,
    onCancel: () => void,
    onApply: () => void,
    onChange: (updatedOptions: T) => void,
  ) => {
    const OptionsComponent = optionsComponent;
    return (
      <OptionsComponent
        options={options}
        onClose={onCancel}
        onSave={onApply}
        onChange={onChange}
      />
    );
  };

  /**
   * Renders the settings panel in the aside
   * Uses the provided renderOptionsPanel function with the current state
   */
  const renderSettingsPanel = () => {
    handleShowViewAside(
      renderOptionsPanel(
        draftOptions,
        handleCancelOptions,
        handleApplyAndCloseOptions,
        handleOptionsChange,
      ),
    );
  };

  /**
   * Renders the history panel in the aside
   */
  const renderHistoryPanel = () => {
    handleShowViewAside(
      <ViewHistory
        settingId={setting.id}
        onClose={handleCancelHistory}
        onSave={handleApplyHistoryAndClose}
        onChange={handleHistoryChange}
      />,
    );
  };

  /**
   * Shows the settings panel in the aside
   */
  const handleShowSettings = () => {
    setIsSettingsPanelOpen(true);
    setIsHistoryPanelOpen(false);
    renderSettingsPanel();
  };

  /**
   * Shows the history panel in the aside
   */
  const handleShowHistory = () => {
    setIsHistoryPanelOpen(true);
    setIsSettingsPanelOpen(false);
    renderHistoryPanel();
  };

  /**
   * Updates the draft options without saving to the backend
   * @param updatedOptions - The new options to set
   */
  const handleOptionsChange = (updatedOptions: T) => {
    setDraftOptions(updatedOptions);
  };

  /**
   * Handles when a history item is selected
   * @param historyValue - The historical value to preview
   */
  const handleHistoryChange = (historyValue: Record<string, unknown>) => {
    setDraftOptions(historyValue as T);
  };

  /**
   * Cancels any unsaved changes and closes the settings panel
   */
  const handleCancelOptions = () => {
    // Revert any unsaved changes by resetting draft to the current saved setting
    setDraftOptions((localSetting?.value as T) || defaultOptions);
    setIsSettingsPanelOpen(false);
    handleShowViewAside(null);
  };

  /**
   * Cancels history view and closes the panel
   */
  const handleCancelHistory = () => {
    // Revert to the current setting
    setDraftOptions((localSetting?.value as T) || defaultOptions);
    setIsHistoryPanelOpen(false);
    handleShowViewAside(null);
  };

  /**
   * Applies the selected history and closes the panel
   */
  const handleApplyHistoryAndClose = () => {
    setIsHistoryPanelOpen(false);
    handleShowViewAside(null);
  };

  /**
   * Applies options and closes the settings panel
   */
  const handleApplyAndCloseOptions = () => {
    closeSettingsPanel();
  };

  /**
   * Closes the settings panel
   */
  const closeSettingsPanel = () => {
    setIsSettingsPanelOpen(false);
    handleShowViewAside(null);
  };

  /**
   * Saves options to the backend and shows success notification
   */
  const handleSaveOptions = async () => {
    const savedSetting = {
      ...localSetting,
      value: draftOptions,
      source: AppConfigSettingSource.facility,
    };

    try {
      await updateConfigSetting(savedSetting);
      setLocalSetting(savedSetting);
      setIsDirty(false);
      success(successMessage ?? defaultSuccessMessage);
    } catch (e) {
      const errorDetails = e instanceof Error ? e.message : String(e);
      const finalErrorMessage = t(
        "useViewOptions.finalErrorMessageWithDetails",
        "{{errorMessage}} Error: {{errorDetails}}",
        {
          errorMessage: errorMessage ?? defaultErrorMessage,
          errorDetails: errorDetails,
        },
      );
      error(finalErrorMessage);
    }
  };

  return {
    draftOptions,
    isDirty,
    handleShowSettings,
    handleShowHistory,
    handleOptionsChange,
    handleCancelOptions,
    handleSaveOptions,
  };
}
