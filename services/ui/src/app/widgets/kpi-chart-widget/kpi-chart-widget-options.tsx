import { Analytics, ColorPalette, DataBase } from "@carbon/icons-react";
import {
  ComboBox,
  NumberInput,
  TextInput,
  Toggle,
  useTheme,
} from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";
import type { ChartInfo } from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import type { TimeChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import { ColorDropdown } from "../../components/color-dropdown";
import { DateFormatDropdown } from "../../components/date-format-dropdown/date-format-dropdown";
import { getColorById } from "../../components/echarts/utils/carbon-colors";
import { OptionsAccordionGroup } from "../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../components/options/options-accordion/options-accordion";
import { ThemeMode } from "../../layout/theme";
import type { BaseWidgetOptionsProps } from "../widget.types";
import type { KpiChartWidgetOptions, ChartStyle } from "./types";
import { useTranslation } from "react-i18next";
import { datePeriodOptions } from "../../views/dashboard/components/dashboard-settings";

export const KpiChartWidgetOptionsForm = ({
  options,
  onChange,
}: BaseWidgetOptionsProps<KpiChartWidgetOptions>) => {
  const chartOptions = options;
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;
  const { t } = useTranslation();

  const [_selected, setSelected] = useState<ChartInfo | null>(null);

  const { data: timeChartInfo } = useQuery({
    queryKey: ["time-chart-info"],
    queryFn: () => chartResolver.getChartInfo(),
  });

  useEffect(() => {
    if (timeChartInfo) {
      const selectedMetric = timeChartInfo?.find(
        (info) => info.id === chartOptions.type,
      );
      setSelected(selectedMetric || null);
    }
  }, [timeChartInfo, chartOptions.type]);

  const handleChartTypeChange = (selectedId: string | null) => {
    if (!selectedId) {
      return;
    }

    const newChartType = selectedId as TimeChartType;
    const selectedChart = timeChartInfo?.find((info) => info.id === selectedId);
    if (selectedChart) {
      onChange({
        ...chartOptions,
        type: newChartType,
      });
    }
  };

  return (
    <OptionsAccordion>
      <OptionsAccordionGroup
        id={t("kpiChartWidgetOptions.displayId", "display")} // Note: Translating IDs might be complex if used elsewhere
        title={t("kpiChartWidgetOptions.displayTitle", "Display")}
        icon={<Analytics size="24" />}
      >
        <TextInput
          labelText={t("kpiChartWidgetOptions.chartTitle", "Chart Title")}
          id="chart-title"
          value={chartOptions.title}
          onChange={(e) => onChange({ ...chartOptions, title: e.target.value })}
        />
        <TextInput
          labelText={t(
            "kpiChartWidgetOptions.kpiValueLabel",
            "KPI Value Label",
          )}
          id="value-label"
          value={chartOptions.valueLabel || ""}
          onChange={(e) =>
            onChange({ ...chartOptions, valueLabel: e.target.value })
          }
          placeholder={t(
            "kpiChartWidgetOptions.kpiValuePlaceholder",
            "Label displayed below the KPI value",
          )}
        />
        <NumberInput
          label={t("kpiChartWidgetOptions.precisionLabel", "Precision")}
          id="precision"
          value={chartOptions.precision ?? 2}
          onChange={(_e, { value }) =>
            onChange({ ...chartOptions, precision: Number(value) })
          }
          min={0}
          max={10}
        />
        <Toggle
          labelText={t("kpiChartWidgetOptions.showUnit", "Show Unit")}
          id="show-unit"
          toggled={chartOptions.showUnit || false}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              showUnit: toggled,
            })
          }
        />
        <Toggle
          labelText={t(
            "kpiChartWidgetOptions.showCurrentKpi",
            "Show Current KPI",
          )}
          id="show-current-kpi"
          toggled={chartOptions.showCurrentKpi || false}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              showCurrentKpi: toggled,
            })
          }
        />
        <Toggle
          labelText={t("kpiChartWidgetOptions.showMinKpi", "Show Min KPI")}
          id="show-min-kpi"
          toggled={chartOptions.showMinKpi || false}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              showMinKpi: toggled,
            })
          }
        />
        <Toggle
          labelText={t("kpiChartWidgetOptions.showMaxKpi", "Show Max KPI")}
          id="show-max-kpi"
          toggled={chartOptions.showMaxKpi || false}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              showMaxKpi: toggled,
            })
          }
        />
        <Toggle
          labelText={t(
            "kpiChartWidgetOptions.showAverageKpi",
            "Show Average KPI",
          )}
          id="show-average-kpi"
          toggled={chartOptions.showAverageKpi || false}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              showAverageKpi: toggled,
            })
          }
        />
        <Toggle
          labelText={t("kpiChartWidgetOptions.showTotalKpi", "Show Total KPI")}
          id="show-total-kpi"
          toggled={chartOptions.showTotalKpi || false}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              showTotalKpi: toggled,
            })
          }
        />
        <Toggle
          labelText="Show Legend"
          id="show-legend"
          toggled={chartOptions.showLegend !== false}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              showLegend: toggled,
            })
          }
        />
      </OptionsAccordionGroup>
      <OptionsAccordionGroup
        id="data"
        title={t("kpiChartWidgetOptions.dataTitle", "Data")}
        icon={<DataBase size="24" />}
      >
        <ComboBox
          titleText={t("kpiChartWidgetOptions.chartType", "Chart Type")}
          id="chart-type"
          items={
            timeChartInfo
              ? [...timeChartInfo]
                  .sort((a, b) => a.title.localeCompare(b.title))
                  .map((info) => ({
                    id: info.id,
                    // Assuming info.title is already a translated value or a key itself
                    text: info.title,
                  }))
              : []
          }
          selectedItem={
            chartOptions.type
              ? {
                  id: chartOptions.type,
                  text:
                    timeChartInfo?.find((info) => info.id === chartOptions.type)
                      ?.title || "",
                }
              : null
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              handleChartTypeChange(selectedItem.id);
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
        />
        <Toggle
          labelText="Override Dashboard Date Range"
          id="override-dashboard-date-range"
          toggled={chartOptions.hasDateRangeOverride || false}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              hasDateRangeOverride: toggled,
              // Set date period to undefined if override is toggled off
              datePeriod: toggled ? chartOptions.datePeriod : undefined,
            })
          }
        />
        {chartOptions.hasDateRangeOverride && (
          <ComboBox
            id="widget-date-range"
            titleText="Date Range Override"
            helperText="The date range to use for the widget"
            items={datePeriodOptions(t)}
            initialSelectedItem={chartOptions.datePeriod}
            onChange={({ selectedItem }) => {
              if (selectedItem) {
                onChange({ ...chartOptions, datePeriod: selectedItem });
              }
            }}
            placeholder="Select a date range"
          />
        )}
      </OptionsAccordionGroup>
      <OptionsAccordionGroup
        id="styling"
        title={t("kpiChartWidgetOptions.stylingTitle", "Styling")}
        icon={<ColorPalette size="24" />}
      >
        <ComboBox
          titleText={t("kpiChartWidgetOptions.chartStyle", "Chart Style")}
          id="chart-style"
          items={[
            { id: "line", text: t("kpiChartWidgetOptions.styleLine", "Line") },
            { id: "area", text: t("kpiChartWidgetOptions.styleArea", "Area") },
            {
              id: "column",
              text: t("kpiChartWidgetOptions.styleColumn", "Column"),
            },
          ]}
          selectedItem={
            chartOptions.chartStyle
              ? {
                  id: chartOptions.chartStyle,
                  text:
                    chartOptions.chartStyle.charAt(0).toUpperCase() +
                    chartOptions.chartStyle.slice(1).replace("-", " "), // This part might need more robust translation logic if styles change
                }
              : {
                  id: "line",
                  text: t("kpiChartWidgetOptions.styleLine", "Line"),
                }
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              onChange({
                ...chartOptions,
                chartStyle: selectedItem.id as ChartStyle,
              });
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
        />

        <ColorDropdown
          id="line-color"
          titleText={t("kpiChartWidgetOptions.color", "Color")}
          selectedColorId={chartOptions.colorId}
          onChange={(colorId) => {
            onChange({
              ...chartOptions,
              colorId: colorId || undefined,
              lineColor: colorId ? getColorById(colorId, isDark) : undefined,
            });
          }}
          showSelectedSwatch={true}
          allowDeselect={true}
        />
        <DateFormatDropdown
          id="date-format"
          titleText="Date Format"
          selectedDateFormat={chartOptions.dateFormat}
          onChange={(dateFormat) => {
            onChange({ ...chartOptions, dateFormat });
          }}
        />
        <Toggle
          labelText={t(
            "kpiChartWidgetOptions.showAverageLine",
            "Show Average Line",
          )}
          id="show-average-line"
          toggled={chartOptions.showAverageLine || false}
          onToggle={(toggled) =>
            onChange({
              ...chartOptions,
              showAverageLine: toggled,
            })
          }
        />
        <div>
          <Toggle
            labelText={t(
              "kpiChartWidgetOptions.showTargetLine",
              "Show Target Line",
            )}
            id="show-target-line"
            toggled={chartOptions.showTargetLine || false}
            onToggle={(toggled) =>
              onChange({
                ...chartOptions,
                showTargetLine: toggled,
              })
            }
          />
          {chartOptions.showTargetLine && (
            <NumberInput
              label={t("kpiChartWidgetOptions.targetValue", "Target Value")}
              id="target-value"
              value={chartOptions.targetValue || 0}
              onChange={(_e, { value }) =>
                onChange({ ...chartOptions, targetValue: Number(value) })
              }
              min={0}
            />
          )}
        </div>
      </OptionsAccordionGroup>
    </OptionsAccordion>
  );
};

export default KpiChartWidgetOptionsForm;
