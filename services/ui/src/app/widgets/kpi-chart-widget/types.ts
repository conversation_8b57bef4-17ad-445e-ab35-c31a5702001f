import { DatePeriodOption } from "../../types/date-types";

/**
 * Chart style options for the KPI Chart Widget
 */
export type ChartStyle =
  | "line"
  | "area"
  | "column"
  | "stacked-area"
  | "stacked-column";

/**
 * Configuration options for the KPI Chart Widget
 */
export interface KpiChartWidgetOptions {
  /**
   * The title of the widget
   */
  title: string;

  /**
   * The type of chart to display
   */
  type?: string;

  /**
   * The format to display the date in
   */
  dateFormat?: Intl.DateTimeFormatOptions;

  /**
   * Additional filters to apply to the chart data
   */
  filters?: Record<string, unknown>;

  /**
   * Color for the chart elements (line, area, or columns)
   */
  lineColor?: string;

  /**
   * Label for the KPI value
   */
  valueLabel?: string;

  /**
   * Visual style of the chart (line, area, or column)
   */
  chartStyle?: ChartStyle;

  /**
   * Whether to show a horizontal line representing the average value
   */
  showAverageLine?: boolean;

  /**
   * Whether to show a horizontal line representing a target value
   */
  showTargetLine?: boolean;

  /**
   * The target value to display as a horizontal line
   */
  targetValue?: number;

  /**
   * Whether to show the unit in the KPI value, tooltip, and reference lines
   */
  showUnit?: boolean;

  /**
   * Number of decimal places to show in the KPI value
   */
  precision?: number;

  /**
   * Whether to show the minimum KPI value
   */
  showMinKpi?: boolean;

  /**
   * Whether to show the maximum KPI value
   */
  showMaxKpi?: boolean;

  /**
   * Whether to show the average KPI value
   */
  showAverageKpi?: boolean;

  /**
   * Whether to show the current KPI value
   */
  showCurrentKpi?: boolean;

  /**
   * Whether to show the total KPI value
   */
  showTotalKpi?: boolean;

  /**
   * New field to store the selected color ID
   */
  colorId?: string;

  /**
   * Whether the dashboard date range is being overriden by the widget
   */
  hasDateRangeOverride?: boolean;

  /**
   * Date range used by the widget if overriding the dashboard date range
   */
  datePeriod?: DatePeriodOption;

  /**
   * Whether to show the legend for the chart
   */
  showLegend?: boolean;
  [key: string]: unknown;
}
