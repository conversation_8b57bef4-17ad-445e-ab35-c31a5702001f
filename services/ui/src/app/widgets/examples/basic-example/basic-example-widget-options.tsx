import { Analytics } from "@carbon/icons-react";
import { NumberInput, TextInput, Toggle } from "@carbon/react";
import { OptionsAccordionGroup } from "../../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../../components/options/options-accordion/options-accordion";
import type { BaseWidgetOptionsProps } from "../../widget.types";
import type { BasicExampleWidgetOptions } from "./types";

/**
 * Example Widget Options form
 * @param baseWidgetOptions - The base widget options
 * @returns The widget options form
 */
export const BasicExampleWidgetOptionsForm = ({
  options,
  onChange,
}: BaseWidgetOptionsProps<BasicExampleWidgetOptions>) => {
  const basicExampleOptions = options;

  return (
    <OptionsAccordion>
      <OptionsAccordionGroup
        id="display"
        title="Display"
        icon={<Analytics size="24" />}
      >
        <TextInput
          labelText="String Option"
          id="string-option"
          value={basicExampleOptions.stringOption}
          onChange={(e) =>
            onChange({ ...basicExampleOptions, stringOption: e.target.value })
          }
        />
        <NumberInput
          label="Number Option"
          id="number-option"
          value={basicExampleOptions.numberOption}
          onChange={(_e, { value }) => {
            onChange({ ...basicExampleOptions, numberOption: Number(value) });
          }}
        />
        <Toggle
          labelText="Boolean Option"
          id="boolean-option"
          toggled={basicExampleOptions.booleanOption}
          onToggle={(toggled) =>
            onChange({ ...basicExampleOptions, booleanOption: toggled })
          }
        />
      </OptionsAccordionGroup>
    </OptionsAccordion>
  );
};

// Widgets must default export the component
export default BasicExampleWidgetOptionsForm;
