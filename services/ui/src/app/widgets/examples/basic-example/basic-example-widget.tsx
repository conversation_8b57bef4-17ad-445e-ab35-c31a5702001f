import { Section, Stack } from "@carbon/react";
import { WidgetContainer } from "../../../components/widget-container/widget-container";
import type { BaseWidgetProps } from "../../widget.types";
import type { BasicExampleWidgetOptions } from "./types";

/**
 * Types -
 *  Widget props extend BaseWidgetProps
 *    - "options" is of type BasicExampleWidgetOptions, which extends
 *      BaseWidgetOptionsProps
 */
interface BasicExampleWidgetProps
  extends BaseWidgetProps<BasicExampleWidgetOptions> {
  options: BasicExampleWidgetOptions;
}

/**
 * Example basic widget component
 * @param options - The widget options
 * @param filters - The widget filters
 * @returns The widget component
 */
export const BasicExampleWidget = ({
  options,
  filters,
}: BasicExampleWidgetProps) => {
  return (
    <WidgetContainer title="Example (Basic)">
      <Stack gap={2} style={{ width: "100%" }}>
        <Section>
          <p>Filters: {JSON.stringify(filters)}</p>
        </Section>
        <Section>
          <p>String option: {options.stringOption ?? "No string option"}</p>
          <p>Number option: {options.numberOption ?? "No number option"}</p>
          <p>Boolean option: {options.booleanOption ? "true" : "false"}</p>
        </Section>
      </Stack>
    </WidgetContainer>
  );
};

// Widgets must default export the component
export default BasicExampleWidget;
