import {
  ChartAverage,
  ChartBar,
  ChartMultitype,
  ChartPie,
  CloudMonitoring,
  CloudRegistry,
  DataTable,
  IbmInstana,
  InformationSquare,
  SummaryKpi,
  UserMultiple,
  ChartCombo,
} from "@carbon/icons-react";
import { lazy } from "react";
import type { WidgetDefinition } from "./widget.types";

export const widgets = [
  {
    id: "kpi",
    name: "KPI",
    description: "Displays a single KPI value with ability to set a target",
    tags: ["kpi", "core"],
    icon: SummaryKpi,
    component: lazy(() => import("./kpi-widget/kpi-widget")),
    optionsComponent: lazy(() => import("./kpi-widget/kpi-widget-options")),
  } as unknown as WidgetDefinition,
  {
    id: "total-stations",
    name: "Total Stations",
    description: "Displays summary information about workstations.",
    tags: ["workstation", "kpi"],
    icon: CloudRegistry,
    component: lazy(
      () => import("./workstation/total-stations-widget/total-stations-widget"),
    ),
  } as unknown as WidgetDefinition,
  {
    id: "station-performance",
    name: "Station Performance",
    description: "Displays station performance metrics.",
    tags: ["workstation", "kpi"],
    icon: IbmInstana,
    component: lazy(
      () =>
        import(
          "./workstation/station-performance-widget/station-performance-widget"
        ),
    ),
  } as unknown as WidgetDefinition,
  {
    id: "logged-in-operators",
    name: "Logged In Operators",
    description: "Displays the number of logged in operators.",
    tags: ["workstation", "kpi"],
    icon: UserMultiple,
    component: lazy(
      () =>
        import(
          "./workstation/logged-in-operators-widget/logged-in-operators-widget"
        ),
    ),
  } as unknown as WidgetDefinition,
  {
    id: "station-health",
    name: "Station Health",
    description: "Displays station health metrics.",
    tags: ["workstation", "kpi"],
    icon: CloudMonitoring,
    component: lazy(
      () => import("./workstation/station-health-widget/station-health-widget"),
    ),
  } as unknown as WidgetDefinition,
  {
    id: "workstation-list",
    name: "Workstation List",
    description: "Displays workstations in tabular format.",
    tags: ["workstation", "table"],
    icon: CloudRegistry,
    component: lazy(
      () =>
        import("./workstation/workstation-list-widget/workstation-list-widget"),
    ),
  } as unknown as WidgetDefinition,
  {
    id: "workstation-order-status",
    name: "Workstation Order Status",
    description: "Displays workstation order status.",
    tags: ["workstation", "workstation"],
    icon: CloudRegistry,
    component: lazy(
      () =>
        import(
          "./workstation/workstation-order-status-widget/workstation-order-status-widget"
        ),
    ),
  } as unknown as WidgetDefinition,
  {
    id: "advice-list",
    name: "Advice List",
    description: "Displays a tabular list of advices.",
    tags: ["inbound-overview", "table"],
    icon: DataTable,
    component: lazy(
      () =>
        import("./inventory-overview/advice-list-widget/advice-list-widget"),
    ),
  } as unknown as WidgetDefinition,
  {
    id: "kpi-chart",
    name: "KPI Chart",
    description:
      "Displays time series data as well as a summary KPI of the data.",
    tags: ["core", "chart", "kpi"],
    icon: ChartAverage,
    component: lazy(() => import("./kpi-chart-widget/kpi-chart-widget")),
    optionsComponent: lazy(
      () => import("./kpi-chart-widget/kpi-chart-widget-options"),
    ),
  } as unknown as WidgetDefinition,
  {
    id: "bar-chart",
    name: "Bar Chart",
    description: "Displays a bar chart, grouping categorical information.",
    tags: ["core", "chart"],
    icon: ChartBar,
    component: lazy(() => import("./bar-chart-widget/bar-chart-widget")),
    optionsComponent: lazy(
      () => import("./bar-chart-widget/bar-chart-widget-options"),
    ),
  } as unknown as WidgetDefinition,
  {
    id: "basic-example",
    name: "Basic Example",
    description: "Displays a basic example of a widget.",
    tags: ["example"],
    icon: InformationSquare,
    component: lazy(
      () => import("./examples/basic-example/basic-example-widget"),
    ),
    optionsComponent: lazy(
      () => import("./examples/basic-example/basic-example-widget-options"),
    ),
  } as unknown as WidgetDefinition,
  {
    id: "outbound-overview-areas",
    name: "Outbound Overview Areas",
    description: "Displays an area tab with metrics for each area.",
    tags: ["outbound-overview", "kpi", "chart"],
    icon: ChartMultitype,
    component: lazy(
      () => import("./outbound-overview-areas/outbound-overview-areas-widget"),
    ),
    optionsComponent: lazy(
      () =>
        import(
          "./outbound-overview-areas/outbound-overview-areas-widget-options"
        ),
    ),
  } as unknown as WidgetDefinition,
  {
    id: "combo-chart",
    name: "Combo Chart",
    description:
      "Displays multiple data series with different chart types in a single chart.",
    tags: ["core", "chart"],
    icon: ChartCombo,
    component: lazy(() => import("./combo-chart-widget/combo-chart-widget")),
    optionsComponent: lazy(
      () => import("./combo-chart-widget/combo-chart-widget-options"),
    ),
  } as unknown as WidgetDefinition,
  {
    id: "pie-chart",
    name: "Pie Chart",
    description: "Displays a pie chart.",
    tags: ["core", "chart"],
    icon: ChartPie,
    component: lazy(() => import("./pie-chart-widget/pie-chart-widget")),
    optionsComponent: lazy(
      () => import("./pie-chart-widget/pie-chart-widget-options"),
    ),
  } as unknown as WidgetDefinition,
];
