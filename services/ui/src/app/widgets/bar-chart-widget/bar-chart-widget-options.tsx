import { Analytics, DataBase, SortDescending } from "@carbon/icons-react";
import { ComboBox, NumberInput, TextInput, Toggle } from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";
import type { ChartInfo } from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import type { CategoryChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import { ColorDropdown } from "../../components/color-dropdown";
import { OptionsAccordionGroup } from "../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../components/options/options-accordion/options-accordion";
import type { BaseWidgetOptionsProps } from "../widget.types";
import type { BarChartWidgetOptions } from "./types";
import { useTranslation } from "react-i18next";
import { DatePeriod } from "../../types";
import {
  BarOrientation,
  SortOrder,
} from "../../components/bar-chart/bar-chart-component";

export const BarChartWidgetOptionsForm = ({
  options,
  onChange,
}: BaseWidgetOptionsProps<BarChartWidgetOptions>) => {
  const barChartOptions = options;
  const { t } = useTranslation();
  const [selectedChart, setSelectedChart] = useState<ChartInfo | null>(null);

  const { data: categoryChartInfo } = useQuery({
    queryKey: ["category-chart-info"],
    queryFn: () => chartResolver.getCategoryChartInfo(),
  });

  useEffect(() => {
    if (categoryChartInfo) {
      const selectedMetric = categoryChartInfo?.find(
        (info) => info.id === barChartOptions.type,
      );
      setSelectedChart(selectedMetric || null);
    }
  }, [categoryChartInfo, barChartOptions.type]);

  const handleChartTypeChange = (selectedId: string | null) => {
    if (!selectedId) {
      return;
    }

    const newChartType = selectedId as CategoryChartType;
    const selected = categoryChartInfo?.find((info) => info.id === selectedId);
    if (selected) {
      onChange({
        ...barChartOptions,
        type: newChartType,
        groupBy: "",
      });
    }
  };

  const handleGroupByChange = (selectedGroupBy: string | null) => {
    if (!selectedGroupBy) {
      return;
    }

    onChange({
      ...options,
      filters: {
        datePeriodRange: options.filters?.datePeriodRange || DatePeriod.today,
        ...options.filters,
        groupBy: selectedGroupBy,
      },
    });
  };

  const handleOrientationChange = (selectedOrientation: string | null) => {
    if (!selectedOrientation) {
      return;
    }

    onChange({
      ...barChartOptions,
      orientation: selectedOrientation as BarOrientation,
    });
  };

  const handleSortByChange = (selectedSortBy: string | null) => {
    if (!selectedSortBy) {
      return;
    }

    onChange({
      ...barChartOptions,
      sortBy: selectedSortBy as SortOrder,
      sortByName: "none",
    });
  };

  const handleSortByNameChange = (selectedSortByName: string | null) => {
    if (!selectedSortByName) {
      return;
    }

    onChange({
      ...barChartOptions,
      sortByName: selectedSortByName as SortOrder,
      sortBy: "none",
    });
  };
  const groupByOptions = selectedChart?.groupBy || [];

  // Ensure sortBy and sortByName have default values
  const currentSortBy = barChartOptions.sortBy || "none";
  const currentSortByName = barChartOptions.sortByName || "none";

  return (
    <OptionsAccordion>
      <OptionsAccordionGroup
        id="display"
        title={t("barChartWidgetOptions.displayTitle", "Display")}
        icon={<Analytics size="24" />}
      >
        <TextInput
          labelText={t("barChartWidgetOptions.chartTitle", "Chart Title")}
          id="chart-title"
          value={barChartOptions.title}
          onChange={(e) =>
            onChange({ ...barChartOptions, title: e.target.value })
          }
        />
        <ComboBox
          titleText={t("barChartWidgetOptions.orientation", "Orientation")}
          id="orientation"
          items={[
            {
              id: "vertical",
              text: t(
                "barChartWidgetOptions.orientationVertical",
                "Vertical (Columns)",
              ),
            },
            {
              id: "horizontal",
              text: t(
                "barChartWidgetOptions.orientationHorizontal",
                "Horizontal (Bars)",
              ),
            },
          ]}
          selectedItem={
            barChartOptions.orientation
              ? {
                  id: barChartOptions.orientation,
                  text:
                    barChartOptions.orientation === "vertical"
                      ? t(
                          "barChartWidgetOptions.orientationVertical",
                          "Vertical (Columns)",
                        )
                      : t(
                          "barChartWidgetOptions.orientationHorizontal",
                          "Horizontal (Bars)",
                        ),
                }
              : {
                  id: "vertical",
                  text: t(
                    "barChartWidgetOptions.orientationVertical",
                    "Vertical (Columns)",
                  ),
                }
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              handleOrientationChange(selectedItem.id);
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
        />

        <ColorDropdown
          id="bar-color"
          titleText={t("barChartWidgetOptions.barColor", "Bar Color")}
          selectedColorId={barChartOptions.colorId}
          onChange={(colorId) => {
            onChange({
              ...barChartOptions,
              colorId: colorId ?? undefined,
            });
          }}
          showSelectedSwatch={true}
          allowDeselect={true}
        />
      </OptionsAccordionGroup>

      <OptionsAccordionGroup
        id="data"
        title={t("barChartWidgetOptions.dataTitle", "Data")}
        icon={<DataBase size="24" />}
      >
        <ComboBox
          titleText={t("barChartWidgetOptions.chartType", "Chart Type")}
          id="chart-type"
          items={
            categoryChartInfo
              ? [...categoryChartInfo]
                  .sort((a, b) => a.title.localeCompare(b.title))
                  .map((info) => ({
                    id: info.id,
                    text: info.title, // Assuming title is translated or a key
                  }))
              : []
          }
          selectedItem={
            barChartOptions.type
              ? {
                  id: barChartOptions.type,
                  text:
                    categoryChartInfo?.find(
                      (info) => info.id === barChartOptions.type,
                    )?.title || "",
                }
              : null
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              handleChartTypeChange(selectedItem.id);
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
        />
        {barChartOptions.type && groupByOptions.length > 0 && (
          <ComboBox
            titleText={t("barChartWidgetOptions.groupBy", "Group By")}
            id="group-by"
            items={groupByOptions.map((option) => ({
              id: option,
              // Assuming the transformed text here doesn't need translation itself
              text: option
                .split("_")
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(" "),
            }))}
            selectedItem={
              barChartOptions?.filters?.groupBy
                ? {
                    id: barChartOptions.filters.groupBy,
                    text: barChartOptions.filters.groupBy
                      .split("_")
                      .map(
                        (word) => word.charAt(0).toUpperCase() + word.slice(1),
                      )
                      .join(" "),
                  }
                : null
            }
            onChange={({ selectedItem }) => {
              if (selectedItem) {
                handleGroupByChange(selectedItem.id);
              }
            }}
            itemToString={(item) => (item ? item.text : "")}
          />
        )}

        <Toggle
          labelText={t(
            "barChartWidgetOptions.showAverageLine",
            "Show Average Line",
          )}
          id="show-average-line"
          toggled={barChartOptions.showAverageLine || false}
          onToggle={(toggled) =>
            onChange({
              ...barChartOptions,
              showAverageLine: toggled,
            })
          }
        />
        <Toggle
          labelText={t(
            "barChartWidgetOptions.showTargetLine",
            "Show Target Line",
          )}
          id="show-target-line"
          toggled={barChartOptions.showTargetLine || false}
          onToggle={(toggled) =>
            onChange({
              ...barChartOptions,
              showTargetLine: toggled,
            })
          }
        />
        {barChartOptions.showTargetLine && (
          <NumberInput
            label={t("barChartWidgetOptions.targetValue", "Target Value")}
            id="target-value"
            value={barChartOptions.targetValue || 0}
            onChange={(_e, { value }) =>
              onChange({
                ...barChartOptions,
                targetValue: Number(value),
              })
            }
            min={0}
          />
        )}

        <NumberInput
          label="Display Limit"
          helperText="Leave empty to show all items"
          id="display-limit"
          value={barChartOptions.displayLimit || ""}
          onChange={(_e, { value }) =>
            onChange({
              ...barChartOptions,
              displayLimit: value === "" ? undefined : Number(value),
            })
          }
          min={1}
          allowEmpty
        />
      </OptionsAccordionGroup>

      <OptionsAccordionGroup
        id="sorting"
        title={t("barChartWidgetOptions.sortingTitle", "Sorting")}
        icon={<SortDescending size="24" />}
      >
        <ComboBox
          titleText={t("barChartWidgetOptions.sortByValue", "Sort by Value")}
          id="sort-by-value"
          items={[
            { id: "none", text: t("barChartWidgetOptions.sortNone", "None") },
            {
              id: "ascending",
              text: t(
                "barChartWidgetOptions.sortAscLowHigh",
                "Ascending (Low to High)",
              ),
            },
            {
              id: "descending",
              text: t(
                "barChartWidgetOptions.sortDescHighLow",
                "Descending (High to Low)",
              ),
            },
          ]}
          selectedItem={
            currentSortBy
              ? {
                  id: currentSortBy,
                  text:
                    currentSortBy === "none"
                      ? t("barChartWidgetOptions.sortNone", "None")
                      : currentSortBy === "ascending"
                        ? t(
                            "barChartWidgetOptions.sortAscLowHigh",
                            "Ascending (Low to High)",
                          )
                        : t(
                            "barChartWidgetOptions.sortDescHighLow",
                            "Descending (High to Low)",
                          ),
                }
              : {
                  id: "none",
                  text: t("barChartWidgetOptions.sortNone", "None"),
                }
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              handleSortByChange(selectedItem.id);
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
          disabled={currentSortByName !== "none"}
        />
        <ComboBox
          titleText={t("barChartWidgetOptions.sortByName", "Sort by Name")}
          id="sort-by-name"
          items={[
            { id: "none", text: t("barChartWidgetOptions.sortNone", "None") },
            {
              id: "ascending",
              text: t("barChartWidgetOptions.sortAscAZ", "Ascending (A to Z)"),
            },
            {
              id: "descending",
              text: t(
                "barChartWidgetOptions.sortDescZA",
                "Descending (Z to A)",
              ),
            },
          ]}
          selectedItem={
            currentSortByName
              ? {
                  id: currentSortByName,
                  text:
                    currentSortByName === "none"
                      ? t("barChartWidgetOptions.sortNone", "None")
                      : currentSortByName === "ascending"
                        ? t(
                            "barChartWidgetOptions.sortAscAZ",
                            "Ascending (A to Z)",
                          )
                        : t(
                            "barChartWidgetOptions.sortDescZA",
                            "Descending (Z to A)",
                          ),
                }
              : {
                  id: "none",
                  text: t("barChartWidgetOptions.sortNone", "None"),
                }
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              handleSortByNameChange(selectedItem.id);
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
          disabled={currentSortBy !== "none"}
        />
      </OptionsAccordionGroup>
    </OptionsAccordion>
  );
};

export default BarChartWidgetOptionsForm;
