import { useTheme } from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";
import type { CategoryChartData } from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import type { CategoryChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import { BarChartComponent } from "../../components/bar-chart/bar-chart-component";
import {
  getColorById,
  getColorByIndex,
} from "../../components/echarts/utils/carbon-colors";
import { WidgetContainer } from "../../components/widget-container/widget-container";
import { ThemeMode } from "../../layout/theme";
import type { BaseWidgetProps } from "../widget.types";
import styles from "./bar-chart-widget.module.css";
import type { BarChartWidgetOptions } from "./types";
import { useDates } from "../../hooks/use-dates";
interface BarChartWidgetProps extends BaseWidgetProps<BarChartWidgetOptions> {
  options: BarChartWidgetOptions;
}

export const BarChartWidget = ({ options, filters }: BarChartWidgetProps) => {
  const mergedFilters = { ...options.filters, ...filters };
  const { startDate, endDate } = useDates(mergedFilters.datePeriodRange);
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;

  const {
    data: response,
    isLoading,
    error,
  } = useQuery({
    queryKey: [
      `category-chart-${mergedFilters?.datePeriodRange}-${mergedFilters?.groupBy}`,
      options.type,
      options.filters,
      filters.datePeriodRange,
    ],
    queryFn: () =>
      chartResolver.getCategoryChart(
        options.type as CategoryChartType,
        mergedFilters,
        startDate,
        endDate,
      ),
    enabled: !!options.type,
    retry: 0,
  });

  if (!options.type) {
    return <WidgetContainer title={options.title} initializing />;
  }

  if (error) {
    return <WidgetContainer title={options.title} error={error} />;
  }

  if (isLoading || !response) {
    return <WidgetContainer title={options.title} loading />;
  }

  const chartTitle = options.title;

  if (response?.success && !response.data) {
    return <WidgetContainer title={chartTitle} noData />;
  }

  if (response?.success && response.data) {
    const chartData = response.data as CategoryChartData;

    // Determine the color to use
    let barColor: string | undefined;

    if (options.colorId) {
      // If colorId is set, use it to get the color
      barColor = getColorById(options.colorId, isDark);
    } else {
      // If no color is selected (Auto mode), use the default color
      barColor = getColorByIndex(0, isDark);
    }

    return (
      <WidgetContainer title={chartTitle}>
        <div className={styles.container}>
          <BarChartComponent
            chartData={chartData}
            orientation={options.orientation}
            showAverageLine={options.showAverageLine}
            showTargetLine={options.showTargetLine}
            targetValue={options.targetValue}
            sortBy={options.sortBy}
            color={barColor}
            displayLimit={options.displayLimit}
          />
        </div>
      </WidgetContainer>
    );
  }

  return null;
};

export default BarChartWidget;
