import type { CategoryChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import {
  BarOrientation,
  SortOrder,
} from "../../components/bar-chart/bar-chart-component";
import type { WidgetFilters } from "../widget.types";

export interface BarChartWidgetOptions {
  type?: CategoryChartType;
  title: string;
  orientation?: BarOrientation;
  showAverageLine?: boolean;
  showTargetLine?: boolean;
  targetValue?: number;
  sortBy?: SortOrder;
  sortByName?: SortOrder;
  colorId?: string;
  displayLimit?: number;
  filters?: WidgetFilters;
  [key: string]: unknown;
}
