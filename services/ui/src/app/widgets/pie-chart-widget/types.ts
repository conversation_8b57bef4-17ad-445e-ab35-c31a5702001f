import type { ApiFilters } from "../../api/api.types";
import type { CategoryChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import { PieType } from "../../components/pie-chart/pie-chart-component";

export interface PieChartWidgetOptions {
  title: string;
  type: CategoryChartType;
  showPercentage?: boolean;
  showLegend?: boolean;
  pieType?: PieType;
  filters?: ApiFilters;
  [key: string]: unknown;
}
