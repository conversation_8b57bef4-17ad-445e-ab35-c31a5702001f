import type { ComponentType, LazyExoticComponent } from "react";
import type { ApiFilters } from "../api/api.types";

/**
 * Base type for widget options - can be extended by specific widgets
 */
export type BaseWidgetOptions = Record<string, unknown>;

/**
 * The base widget props that all Widgets will use.
 */
export interface BaseWidgetProps<
  TOptions extends BaseWidgetOptions = BaseWidgetOptions,
> {
  id: string;
  type: string;
  filters: WidgetFilters;
  options: TOptions;
}

/**
 * The base widget options props that all Widgets will use.
 */
export interface BaseWidgetOptionsProps<
  TOptions extends BaseWidgetOptions = BaseWidgetOptions,
> {
  /**
   * The function to call when the options change
   */
  onChange: (options: TOptions) => void;
  /**
   * Widget-specific options object
   */
  options: TOptions;
}

/**
 * The widget definition to map a widget id to React lazily loaded components.
 * Uses unknown for component types to avoid explicit any while supporting polymorphism.
 */
export interface WidgetDefinition {
  id: string;
  name: string;
  description: string;
  tags: string[];
  icon: ComponentType<{ size?: number }>;
  component: LazyExoticComponent<
    ComponentType<BaseWidgetProps<BaseWidgetOptions>>
  >;
  optionsComponent?: LazyExoticComponent<
    ComponentType<BaseWidgetOptionsProps<BaseWidgetOptions>>
  >;
}

/**
 * Widget-specific filters that extend the base API filters
 */
export type WidgetFilters = ApiFilters & {
  autoRefresh?: {
    enabled: boolean;
    interval: number; // in seconds
  };
};
