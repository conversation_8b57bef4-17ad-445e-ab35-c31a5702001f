import type {
  CategoryChartMapDefinition,
  ChartInfo,
  TimeChartMapDefinition,
} from "./time-chart-resolver-types";

export const categoryChartInfo: ChartInfo[] = [
  {
    id: "faults",
    title: "Faults",
    description: "Equipment faults",
    groupBy: [
      "reason_name",
      "device_code",
      "device_functional_type",
      "level",
      "aisle",
    ],
  },
];

export const categoryChartDefinitions: CategoryChartMapDefinition[] = [
  {
    id: "faults",
    endpoint: "/equipment/faults/grouped/count/series",
    unit: "faults",
    series: [
      {
        id: "faultCounts",
      },
    ],
  },
];

export const timeChartInfo: ChartInfo[] = [
  {
    id: "orders-customer-line-progress",
    title: "Order Line Progress",
    description: "Area order line progress series trend data.",
  },
  {
    id: "orders-customer-line-throughput",
    title: "Order Line Throughput",
    description: "Area order line through put series trend data.",
  },
  {
    id: "orders-facility-line-throughput",
    title: "Facility Line Throughput",
    description: "Facility order line throughput series trend data.",
  },
  {
    id: "inventory-stock-distribution-at-percentage",
    title: "Inventory Stock Distribution at Percentage",
    description:
      "Area inventory stock distribution at percentage series trend data.",
  },
  {
    id: "inventory-stock-distribution-under-percentage",
    title: "Inventory Stock Distribution Under Percentage",
    description:
      "Area inventory stock distribution under percentage series trend data.",
  },
  {
    id: "inventory-stock-distribution-over-percentage",
    title: "Inventory Stock Distribution Over Percentage",
    description:
      "Area inventory stock distribution over percentage series trend data.",
  },
  {
    id: "inventory-stock-distribution-no-percentage",
    title: "Inventory Stock Distribution No Percentage",
    description:
      "Area inventory stock distribution no percentage series trend data.",
  },
  {
    id: "faults",
    title: "Faults",
    description: "Equipment faults",
  },
  {
    id: "daily-replenishments",
    title: "Daily Replenishments",
    description: "Daily replenishments",
  },
  {
    id: "daily-pending-orders",
    title: "Daily Pending Orders",
    description: "Daily pending orders",
  },
  {
    id: "daily-cycle-times",
    title: "Daily Cycle Times",
    description: "Daily cycle times",
  },
  {
    id: "daily-replenishments-by-shift",
    title: "Daily Replenishments by Shift",
    description: "Daily replenishments by shift",
  },
  {
    id: "replenishment-task-type-data",
    title: "Daily Replenishments by Task Type",
    description: "Daily replenishments by task type",
  },
];

export const timeChartDefinitions: TimeChartMapDefinition[] = [
  {
    id: "orders-customer-line-progress",
    endpoint: "/orders/customer/line/progress/series",
    unit: "percent",
    series: [
      {
        id: "progress",
      },
    ],
  },
  {
    id: "orders-customer-line-throughput",
    endpoint: "/orders/customer/line/throughput/series",
    unit: "orders/hr",
    series: [
      {
        id: "throughput",
      },
    ],
  },
  {
    id: "orders-facility-line-throughput",
    endpoint: "/orders/facility/line/throughput/series",
    unit: "orders/hr",
    series: [
      {
        id: "throughput",
      },
    ],
  },
  {
    id: "inventory-stock-distribution-at-percentage",
    endpoint: "/inventory/stock/distribution/at/percentage/series",
    unit: "percent",
    series: [
      {
        id: "atInventory",
      },
    ],
  },
  {
    id: "inventory-stock-distribution-no-percentage",
    endpoint: "/inventory/stock/distribution/no/percentage/series",
    unit: "percent",
    series: [
      {
        id: "noInventory",
      },
    ],
  },
  {
    id: "inventory-stock-distribution-under-percentage",
    endpoint: "/inventory/stock/distribution/under/percentage/series",
    unit: "percent",
    series: [
      {
        id: "underInventory",
      },
    ],
  },
  {
    id: "inventory-stock-distribution-over-percentage",
    endpoint: "/inventory/stock/distribution/over/percentage/series",
    unit: "percent",
    series: [
      {
        id: "overInventory",
      },
    ],
  },
  {
    id: "daily-replenishments",
    endpoint: "/inventory/replenishment/details",
    unit: "replenishments",
    series: [{ id: "dailyReplenishments" }],
  },
  {
    id: "daily-pending-orders",
    endpoint: "/inventory/replenishment/details",
    unit: "orders",
    series: [{ id: "dailyPendingOrders" }],
  },
  {
    id: "daily-cycle-times",
    endpoint: "/inventory/replenishment/details",
    unit: "minutes",
    series: [{ id: "dailyCycleTimes" }],
  },
  {
    id: "daily-replenishments-by-shift",
    endpoint: "/inventory/replenishment/details",
    unit: "replenishments",
    series: [{ id: "shiftData" }],
  },
  {
    id: "replenishment-task-type-data",
    endpoint: "/inventory/replenishment/task-type-series",
    unit: "replenishments",
    series: [{ id: "taskTypeData" }],
  },
];
