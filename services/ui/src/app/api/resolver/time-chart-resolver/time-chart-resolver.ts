import type { PathsWithMethod } from "openapi-typescript-helpers";
import { Logger } from "../../../utils";
import type { ApiFilters } from "../../api.types";
import { ictApi } from "../../ict-api";
import type { paths } from "@ict/sdk/openapi-react-query";
import {
  categoryChartDefinitions,
  categoryChartInfo,
  timeChartDefinitions,
  timeChartInfo,
} from "./time-chart-resolver-data";
import type {
  CategoryChartMapDefinition,
  CategoryDataPoint,
  ChartData,
  ChartInfo,
  ChartSeriesDataResponse,
  TimeChartMapDefinition,
  TimeChartSeriesData,
} from "./time-chart-resolver-types";
import type { CategoryChartType, TimeChartType } from "./time-chart-types";

export class ChartResolver {
  private logger;
  constructor() {
    this.logger = new Logger("ChartResolver");
  }

  private getUrl(chartInfo: ChartInfo) {
    const definition = timeChartDefinitions.find(
      (definition) => definition.id === chartInfo.id,
    );
    if (!definition) {
      throw new Error(`Chart definition not found for id: ${chartInfo.id}`);
    }
    return definition.endpoint;
  }

  private buildRequestBody(
    startDate: Date,
    endDate: Date,
    filters: ApiFilters,
  ): Record<string, unknown> {
    const requestBody: Record<string, unknown> = {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };

    // Add groupBy if it exists in filters
    if (filters.groupBy && typeof filters.groupBy === "string") {
      requestBody.groupByColumn = filters.groupBy;
    }

    // For now, we're ignoring complex filters as mentioned in the requirements
    // We'll handle those in a future update

    return requestBody;
  }

  public async getCategoryChart(
    id: CategoryChartType,
    filters: ApiFilters,
    startDate: Date,
    endDate: Date,
  ): Promise<ChartSeriesDataResponse> {
    this.logger.info("ChartResolver::getChart", id);
    const info = categoryChartInfo.find((info) => info.id === id);
    if (!info) {
      throw new Error(`Chart info not found for id: ${id}`);
    }

    const definition = categoryChartDefinitions.find(
      (definition) => definition.id === id,
    );
    this.logger.info("ChartResolver", definition);
    if (!definition) {
      throw new Error(`Metric definition not found for id: ${id}`);
    }

    const url = definition.endpoint;
    const requestBody = this.buildRequestBody(
      startDate,
      endDate,
      filters,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ) as any;

    const clientUrl = ictApi.client.queryOptions(
      "post",
      definition.endpoint as PathsWithMethod<paths, "post">,
      {
        body: requestBody,
      },
    );

    this.logger.info("ChartResolver api POST request", url, requestBody);
    const response = await ictApi.queryClient.fetchQuery(clientUrl);

    this.logger.info("ChartResolver::getChart - response ", response);

    // Error response
    // if (!response.success) return { success: false, error: response.error };

    // // no data response
    if (!response) return { success: true, data: null };

    // For request-body type, use the category data conversion
    const categoryData = this.convertToCategoryResponse(response, definition);

    const chartData: ChartData = {
      id: id,
      unit: definition.unit,
      categoryData,
    };

    this.logger.info("ChartResolver::getChart - categoryData ", categoryData);

    const res: ChartSeriesDataResponse = {
      success: true,
      data: chartData,
    };

    this.logger.info("ChartResolver::getChart - res ", res);

    // data response
    return res;
  }

  /**
   *
   * @param id
   * @param startDate
   * @param endDate
   * @returns
   */
  public async getChart(
    id: TimeChartType,
    filters: ApiFilters,
    startDate: Date,
    endDate: Date,
  ): Promise<ChartSeriesDataResponse> {
    this.logger.info("ChartResolver::getChart", id);
    const info = timeChartInfo.find((info) => info.id === id);
    if (!info) {
      throw new Error(`Chart info not found for id: ${id}`);
    }

    const definition = timeChartDefinitions.find(
      (definition) => definition.id === id,
    );
    this.logger.info("ChartResolver", definition);
    if (!definition) {
      throw new Error(`Metric definition not found for id: ${id}`);
    }

    let queryParams = {
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
    };

    queryParams = { ...queryParams, ...filters };
    // For query-params type (default), use GET with query parameters
    const url = this.getUrl(info);
    this.logger.info("ChartResolver api GET request", url);
    const clientUrl = ictApi.client.queryOptions(
      "get",
      url as PathsWithMethod<paths, "get">,
      {
        params: { query: queryParams },
      },
    );

    const response = await ictApi.queryClient.fetchQuery(clientUrl);
    this.logger.info("ChartResolver::getChart - response ", response);

    // Error response
    // if (!response.success) return { success: false, error: response.error };

    // no data response
    if (!response) return { success: true, data: null };

    // Convert the response data based on the chart type

    // For query-params type, use the time series conversion
    const seriesData = this.convertToTimeSeriesResponse(response, definition);

    const chartData: ChartData = {
      id: id,
      series: seriesData,
    };

    this.logger.info("ChartResolver::getChart - seriesData ", seriesData);

    const res: ChartSeriesDataResponse = {
      success: true,
      data: chartData,
    };

    this.logger.info("ChartResolver::getChart - res ", res);

    // data response
    return res;
  }

  public async getChartInfo(): Promise<ChartInfo[]> {
    return timeChartInfo;
  }

  public async getCategoryChartInfo(): Promise<ChartInfo[]> {
    return categoryChartInfo;
  }

  /**
   * Converts API response to time series data format
   * Used for traditional time series charts with query-params type
   */
  private convertToTimeSeriesResponse(
    response: unknown,
    definition: TimeChartMapDefinition,
  ): TimeChartSeriesData[] {
    this.logger.info("convertToTimeSeriesResponse", definition);
    const data: TimeChartSeriesData[] = [];

    // Type guard to ensure response is an object
    if (!response || typeof response !== "object") {
      this.logger.warn("Invalid response format - not an object");
      return [];
    }

    const seriesId = definition.series[0].id;
    const series = (response as Record<string, unknown>)[seriesId];

    if (!Array.isArray(series)) {
      this.logger.warn("Invalid series data format");
      return [];
    }

    // Handle the first shape where we have multiple series in an object
    if (
      series.length === 1 &&
      typeof series[0] === "object" &&
      !("name" in series[0])
    ) {
      const seriesObject = series[0];
      for (const [key, points] of Object.entries(seriesObject)) {
        data.push({
          id: key,
          unit: definition.unit,
          data: (points as { name: string; value: number }[]).map((point) => ({
            unit: point.name,
            value: point.value,
          })),
        });
      }
      return data;
    }

    // Handle the second shape where we have a single series of points
    data.push({
      id: seriesId,
      unit: definition.unit,
      data: series.map((point) => ({
        unit: point.name,
        value: point.value,
      })),
    });

    return data;
  }

  /**
   * Converts API response to category data format
   * Used for category-based charts with request-body type
   */
  private convertToCategoryResponse(
    response: unknown,
    definition: CategoryChartMapDefinition,
  ): CategoryDataPoint[] {
    this.logger.info("convertToCategoryResponse", response);

    // Type guard to ensure response is an object
    if (!response || typeof response !== "object") {
      this.logger.warn("Invalid response format - not an object");
      return [];
    }

    // Get the first series ID from the definition
    // For category charts, we typically have just one series
    if (definition.series.length === 0) {
      this.logger.warn("No series defined for category chart", definition.id);
      return [];
    }

    const seriesId = definition.series[0].id;
    const categoryData = (response as Record<string, unknown>)[seriesId];

    // If the category data doesn't exist or isn't an array, return empty array
    if (!categoryData || !Array.isArray(categoryData)) {
      this.logger.warn(`Category data not found for series ID: ${seriesId}`);
      return [];
    }

    // Map the category data to the expected format
    return categoryData.map((item: { name: string; value: number }) => ({
      name: item.name,
      value: item.value,
    }));
  }
}

export const chartResolver = new ChartResolver();
