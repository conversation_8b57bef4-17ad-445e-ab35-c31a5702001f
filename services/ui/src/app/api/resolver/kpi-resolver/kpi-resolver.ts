import type { PathsWithMethod } from "openapi-typescript-helpers";
import { Logger } from "../../../utils";
import type { ApiFilters } from "../../api.types";
import { ictApi } from "../../ict-api";
import type { paths } from "@ict/sdk/openapi-react-query";
import { metricDefinitions, metricInfo } from "./kpi-resolver-data";
import {
  type MetricKpiMapDefinition,
  isMetricKpiProgress,
} from "./kpi-resolver-types";
import type { MetricKpiType } from "./kpi-types";
import type { MetricKpiInfo, MetricKpiResponse, MetricKpiValue } from "./types";
import i18n, { i18nInitPromise } from "../../../config/i18n/i18n";

/**
 * This is a temporary class that will be replaced with a more robust "Metric" API in the future.
 *
 * This class contains two endpoints:
 *   * getMetricInfo - return a list of supported metrics
 *   * getMetric - return the value of a specific metric in a standard Metric format
 *
 * The reason to introduce this is that it normalizes all of our Metrics from various endpoints (/orders/shipped) and
 * the various response bodies { ordersShipped: 450 }
 */
export class MetricKpiResolver {
  private logger;
  constructor() {
    this.logger = new Logger("MetricKpiResolver");
  }

  private buildUrl(
    metricInfo: MetricKpiInfo,
    startDate: Date,
    endDate: Date,
    filters: ApiFilters,
  ) {
    const definition = metricDefinitions.find(
      (definition) => definition.id === metricInfo.id,
    );
    if (!definition) {
      throw new Error(`Metric definition not found for id: ${metricInfo.id}`);
    }
    let queryParams = {
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
    };

    // Filter out client-side only properties like autoRefresh
    const { autoRefresh: _autoRefresh, ...apiFilters } =
      filters as ApiFilters & { autoRefresh?: unknown };
    queryParams = { ...queryParams, ...apiFilters };

    return ictApi.client.queryOptions(
      "get",
      definition.endpoint as PathsWithMethod<paths, "get">,
      {
        params: {
          query: queryParams,
        },
      },
    );
  }

  private extractValue(
    response: Record<string, number>,
    definition: MetricKpiMapDefinition,
  ): MetricKpiValue {
    if (isMetricKpiProgress(definition)) {
      return {
        current: response[definition.valueKeys.current],
        total: response[definition.valueKeys.total],
      };
    }

    return response[definition.valueKey];
  }

  async getMetric(
    metricId: MetricKpiType,
    filters: ApiFilters,
    startDate: Date,
    endDate: Date,
  ): Promise<MetricKpiResponse> {
    await i18nInitPromise;
    const translatedMetricInfo = metricInfo(i18n.t);
    const info = translatedMetricInfo.find((info) => info.id === metricId);
    if (!info) {
      throw new Error(`Metric info not found for id: ${metricId}`);
    }

    const definition = metricDefinitions.find(
      (definition) => definition.id === metricId,
    );
    if (!definition) {
      throw new Error(`Metric definition not found for id: ${metricId}`);
    }

    const url = this.buildUrl(info, startDate, endDate, filters);
    const response = await ictApi.queryClient.fetchQuery(url);

    this.logger.info("MetricResolver - response ", response);

    // if (!response.ok) {
    //   throw new Error(
    //     `Failed to fetch metric ${metricId}: ${response.statusText}`,
    //   );
    // }

    if (!response) {
      return {
        result: "204",
        id: metricId,
        label: info.label,
      };
    }

    const data = response;
    const metricResponse = {
      result: "200",
      id: metricId,
      label: info.label,
      value: this.extractValue(data as Record<string, number>, definition),
      unit: definition.unit,
    };

    this.logger.info("MetricResolver - metricResponse ", metricResponse);
    return metricResponse as MetricKpiResponse;
  }

  async getMetricInfo(): Promise<MetricKpiInfo[]> {
    await i18nInitPromise;
    const translatedMetricInfo = metricInfo(i18n.t);
    return translatedMetricInfo;
  }
}

export const metricKpiResolver = new MetricKpiResolver();
