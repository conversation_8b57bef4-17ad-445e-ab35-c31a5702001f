const prefix = "/api/3.6";

export default class Endpoints {
  public static readonly trustedAuth = "/management/auth/trusted";

  public static PostSignIn(): string {
    return `${prefix}/auth/signin`;
  }

  public static GetWorkbooksForSite(siteId: string): string {
    return `${this.GetSiteBase(siteId)}/workbooks`;
  }

  public static GetViewsForSite(siteId: string): string {
    return `${this.GetSiteBase(siteId)}/views?pageSize=1000`;
  }

  public static GetProjectsForSite(siteId: string): string {
    return `${this.GetSiteBase(siteId)}/projects`;
  }

  public static GetAlertsForSite(siteId: string): string {
    return `${this.GetSiteBase(siteId)}/dataAlerts?pageSize=1000`;
  }

  public static GetAlertDetailsForSite(
    siteId: string,
    alertId: string,
  ): string {
    return `${this.GetSiteBase(siteId)}/dataAlerts/${alertId}`;
  }

  private static GetSiteBase(siteId: string) {
    return `${prefix}/sites/${siteId}`;
  }
}
