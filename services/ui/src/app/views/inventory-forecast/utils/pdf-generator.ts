import { jsPDF } from "jspdf";
import { TFunction } from "i18next";
import { logger } from "../../../utils/logger";
import type { InventoryForecastItem, SkuLocationDetail } from "../types";
import { formatISOTimeToReadableDateTime } from "../../../utils/date-util";

type InventoryForecastSkuLocationDetails = SkuLocationDetail;

export interface PDFGeneratorConfig {
  selectedSkus: InventoryForecastItem[];
  skusPerPage: number;
  enteredFileName: string;
  dataUpdateTimestamp?: string;
  analysisPerformedTimestamp?: string;
  getSkuLocationDetails: (
    sku: string,
  ) => Promise<InventoryForecastSkuLocationDetails[]>;
  onComplete: () => void;
  t: TFunction;
  timezone?: string;
}

export class InventoryForecastPDFGenerator {
  private doc = new jsPDF({
    orientation: "portrait",
    unit: "in",
    format: [8.5, 11],
  });
  private readonly leftMargin = 0.25;
  private readonly rightMargin = 8.25;
  private readonly topMargin = 0.25;
  private readonly bottomMargin = 10.75;
  private readonly footerHeight = 0.4;
  private readonly pageHeight =
    this.bottomMargin - this.topMargin - this.footerHeight;

  public async generatePDF(config: PDFGeneratorConfig): Promise<void> {
    const { t, timezone } = config;

    try {
      const dateStr = new Date().toLocaleDateString().replace(/\//g, "-");
      const fileName = `${config.enteredFileName || "Pick_List"}_${dateStr}.pdf`;
      let skuHeight = 0.75;

      const calculateSkuHeight = (
        locations: InventoryForecastSkuLocationDetails[],
        rowHeight = 0.3,
      ) => {
        const baseHeight = 0.3;
        const locationRowsHeight = locations.length * rowHeight;
        return baseHeight + locationRowsHeight;
      };

      const printedSkus = await Promise.all(
        config.selectedSkus.map(async (skuItem) => {
          const skuLocationDetails = await config.getSkuLocationDetails(
            skuItem.sku,
          );
          if (skuLocationDetails?.length) {
            return { skuItem, skuLocationDetails };
          }
          logger.info(
            `Skipping SKU ${skuItem.sku} as it has no Reserve Storage details.`,
          );
          return null;
        }),
      ).then((results) =>
        results.filter(
          (item): item is NonNullable<typeof item> => item !== null,
        ),
      );

      if (printedSkus.length === 0) {
        throw new Error(
          t(
            "pdfGenerator.noReserveLocationsError",
            "No locations found under Reserve Storage for the selected SKU(s). PDF cannot be generated.",
          ),
        );
      }

      let currentPage = 1;
      let currentY = this.topMargin + 0.25;

      currentY = this.addPageHeader(
        fileName,
        config.dataUpdateTimestamp ??
          t("pdfGenerator.noData", "No data available"),
        config.analysisPerformedTimestamp ??
          t("pdfGenerator.noData", "No data available"),
        printedSkus.length,
        currentY,
        t,
        timezone,
      );

      for (const [printedIndex, printedItem] of printedSkus.entries()) {
        const { skuItem, skuLocationDetails } = printedItem;

        skuHeight = calculateSkuHeight(skuLocationDetails);

        let needsPageBreak = false;
        if (config.skusPerPage === 1 && printedIndex > 0) {
          needsPageBreak = true;
        } else if (config.skusPerPage !== 1 && printedIndex > 0) {
          if (currentY + skuHeight + 0.1 > this.pageHeight) {
            needsPageBreak = true;
          }
        }

        if (needsPageBreak) {
          this.addPageFooter(
            this.doc,
            currentPage,
            "",
            this.leftMargin,
            this.rightMargin - 1,
            this.bottomMargin,
            t,
          );
          this.doc.addPage();
          currentPage += 1;
          currentY = this.addPageHeader(
            fileName,
            config.dataUpdateTimestamp ??
              t("pdfGenerator.noData", "No data available"),
            config.analysisPerformedTimestamp ??
              t("pdfGenerator.noData", "No data available"),
            printedSkus.length,
            this.topMargin + 0.25,
            t,
            timezone,
          );
        }
        const isDoubleDigit = printedIndex + 1 >= 10;
        const rectWidth = isDoubleDigit ? 0.35 : 0.3;
        const rectRightExtension = isDoubleDigit ? 0.01 : 0;
        this.doc.setFillColor(0, 0, 0);
        this.doc.roundedRect(
          this.leftMargin,
          currentY - 0.2,
          rectWidth + rectRightExtension,
          0.25,
          0.05,
          0.05,
          "F",
        );
        this.doc.setTextColor(255, 255, 255);
        this.doc.setFont("helvetica", "bold");
        this.doc.setFontSize(14);
        const textOffset = isDoubleDigit ? 0.07 : 0.1;
        this.doc.text(
          `${printedIndex + 1}`,
          this.leftMargin + textOffset,
          currentY,
        );
        this.doc.setTextColor(0, 0, 0);
        this.doc.text(`${skuItem.sku}`, this.leftMargin + 0.5, currentY);
        this.doc.text(
          `${skuItem.forecast.forwardPickTomorrow ?? 0}`,
          this.leftMargin + 2.7,
          currentY,
        );
        currentY += 0.3;

        this.doc.setFontSize(11);
        this.doc.setFont("helvetica", "normal");
        this.doc.text(
          t(
            "pdfGenerator.skuOfTotal",
            `SKU ${printedIndex + 1} of ${printedSkus.length}`,
            { currentSku: printedIndex + 1, totalSkus: printedSkus.length },
          ),
          this.leftMargin + 0.5,
          currentY,
        );
        this.doc.text(
          t("pdfGenerator.forecastLabel", `Forecast Forward Pick Tomorrow`),
          this.leftMargin + 2.7,
          currentY,
        );
        currentY += 0.2;

        this.doc.setDrawColor(200, 200, 200);
        this.doc.setLineWidth(0.01);
        this.doc.line(this.leftMargin, currentY, this.rightMargin, currentY);
        currentY += 0.3;

        this.doc.setFontSize(10);
        this.doc.setFont("helvetica", "bold");
        this.doc.text(t("pdfGenerator.locationId", "Location ID"), 1, currentY);
        this.doc.text(
          t("pdfGenerator.containers", "Containers"),
          2.1,
          currentY,
        );
        this.doc.text(
          t("pdfGenerator.skuQuantity", "SKU Quantity"),
          3,
          currentY,
        );
        this.doc.text(
          t("pdfGenerator.caseQuantity", "Case Quantity"),
          4.1,
          currentY,
        );
        this.doc.text(
          t("pdfGenerator.casesRequired", "Cases Required"),
          5.2,
          currentY,
        );
        this.doc.text(
          t("pdfGenerator.conditionCode", "Condition Code"),
          6.5,
          currentY,
        );
        currentY += 0.4;

        const locationIconBase64 =
          "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAoVJREFUeF7tmoFOwzAMRLMvA74M+DLGlwGWmilkW5c7n51qbaVJk9YkvuezkxZOZefXaef6ywHgcMDOCcwogddSin3seimlfDc5OP/9Zp+0KwNAFfveCH8k0CAYmI9HN3p/jwZgAky45/qMBBEFQCG8hxYCIgJAhPgKQw5BDeALqHO2LKw/vLGD+3FKABnia/wyCCoAkba/l2xJOSgAzBAv6wkKAD+qeiTncWlwDV72Z+8+T+q+DHOVghfA7OxXCrYrUEdoDwBP7ddgLXvtUZl1wxQA7La3ZlkWKr0tehzA2H8kU+YIg4telBZq0GJbNEikWTFOGIF7BZUFwASIroWWGAL4AgINqg5EATDB7R4A2guoRsg6AM0OU58ZLqNfi6PBMdlB12DKbNMA0G02FQBan9Y8kQDR7Nv8TJnRDrAF0QyNBsnAtbmpfkYNWvZCtBGOPMMzma/zUlqoQU4ALQj7bg2yPlLXByP0GIyU17+5PQBYq6LiRu6n6p+umyYitgxGRCH30ImkBy7ReWoWEbh2L21/hQO2UAauJLoGi5qhxwmu7CscYHPMdIE7ge4JJrrAnX2VA2a5YFMADELmligRr3RAtgtUpcs9QKy07YxzgSz7agdULtGlIMt+FIDIbVGa/SgAUQ1RLj4SgM3NvDBZOxVKrV8XCpk04EEpJPvRDlCVQpj4DACKUoh0qfwccKuGPbtCaPazHGDrMAekcPGZANB+wPwliXqvEFpfXURIKaTFlbbQAmMEQor1M84B9yy51g9SxWf3gBbIrQemdPEzAfRNMa3p9bbM7gH9+vV5YVoc0xZumqJ9pf7Lk9r3ukGzASg0uOY4ALjwPcHgwwFPkESXhF9UJ3tBwZ3XfwAAAABJRU5ErkJggg==";

        const forwardPickTomorrow = Math.abs(
          skuItem.forecast.forwardPickTomorrow ?? 0,
        );
        let remainingQuantityNeeded = forwardPickTomorrow;

        skuLocationDetails.forEach((detail, detailIndex) => {
          const containerCount = detail.containerCount ?? 1;
          const caseQuantity = detail.containerQuantity ?? 1;

          const rowData = {
            icon: locationIconBase64,
            locationId: detail.locationId,
            containers: `${containerCount}`,
            quantity: t("pdfGenerator.eaches", `{{quantity}} eaches`, {
              quantity: detail.quantity ?? 0,
            }),
            caseQuantity: detail.containerQuantity,
            casesRequired: "",
            conditionCode: detail.conditionCode,
          };

          if (remainingQuantityNeeded > 0 && caseQuantity > 0) {
            const quantityAvailableAtLocation = detail.quantity ?? 0;
            const quantityToFulfillFromThisLocation = Math.min(
              remainingQuantityNeeded,
              quantityAvailableAtLocation,
            );

            if (quantityToFulfillFromThisLocation > 0) {
              const casesRequiredForRow = Math.ceil(
                quantityToFulfillFromThisLocation / caseQuantity,
              );
              const actualCasesRequired = Math.min(
                casesRequiredForRow,
                containerCount,
              );
              rowData.casesRequired = actualCasesRequired.toString();
              remainingQuantityNeeded -= actualCasesRequired * caseQuantity;
            } else {
              rowData.casesRequired = "0";
            }
          } else {
            rowData.casesRequired = "0";
          }
          remainingQuantityNeeded = Math.max(0, remainingQuantityNeeded);

          this.doc.addImage(
            rowData.icon,
            "PNG",
            this.leftMargin + 0.5,
            currentY - 0.15,
            0.2,
            0.2,
          );

          this.doc.setFont("helvetica", "normal");
          this.doc.text(rowData.locationId || "", 1, currentY);
          this.doc.text(rowData.containers || "", 2.1, currentY);
          this.doc.text(rowData.quantity || "", 3, currentY);
          this.doc.text(`${rowData.caseQuantity ?? ""}`, 4.1, currentY);
          this.doc.text(rowData.casesRequired || "", 5.2, currentY);
          this.doc.text(rowData.conditionCode || "", 6.5, currentY);
          currentY += 0.2;

          // Page break logic within the loop
          if (currentY + 0.3 > this.pageHeight) {
            this.addPageFooter(
              this.doc,
              currentPage,
              "",
              this.leftMargin,
              this.rightMargin - 1,
              this.bottomMargin,
              t,
            );
            this.doc.addPage();
            currentPage += 1;
            currentY = this.topMargin + 0.5;
            this.doc.setFontSize(10);
            this.doc.setFont("helvetica", "bold");
            this.doc.text(
              t("pdfGenerator.locationId", "Location ID"),
              1,
              currentY,
            );
            this.doc.text(
              t("pdfGenerator.containers", "Containers"),
              2.1,
              currentY,
            );
            this.doc.text(
              t("pdfGenerator.skuQuantity", "SKU Quantity"),
              3,
              currentY,
            );
            this.doc.text(
              t("pdfGenerator.caseQuantity", "Case Quantity"),
              4.1,
              currentY,
            );
            this.doc.text(
              t("pdfGenerator.casesRequired", "Cases Required"),
              5.2,
              currentY,
            );
            this.doc.text(
              t("pdfGenerator.conditionCode", "Condition Code"),
              6.5,
              currentY,
            );
            currentY += 0.4;
          }

          if (detailIndex < skuLocationDetails.length - 1) {
            this.doc.setDrawColor(200, 200, 200);
            this.doc.setLineWidth(0.01);
            this.doc.line(1, currentY, this.rightMargin, currentY);
            currentY += 0.3;
          } else {
            currentY += 0.1;
          }
        });

        this.doc.setDrawColor(0, 0, 0);
        this.doc.setLineWidth(0.02);
        this.doc.line(this.leftMargin, currentY, this.rightMargin, currentY);
        currentY += 0.4;
      }

      const totalPages = this.doc.getNumberOfPages();

      for (let i = 1; i <= totalPages; i++) {
        this.doc.setPage(i);
        this.addPageFooter(
          this.doc,
          i,
          totalPages,
          this.leftMargin,
          this.rightMargin - 1,
          this.bottomMargin,
          t,
        );
      }

      this.doc.save(fileName);
      config.onComplete();
    } catch (error) {
      logger.error(`Failed to generate PDF: ${error}`);
      throw error;
    }
  }

  public addPageHeader(
    fileName: string,
    dataUpdatedTimestamp: string,
    analysisPerformedTimestamp: string,
    printedSkuCount: number,
    currentY: number,
    t: TFunction,
    timezone?: string,
  ): number {
    let updatedY = currentY;

    this.doc.setFontSize(18);
    this.doc.setFont("helvetica", "bold");
    this.doc.text(
      fileName.replace(/_\d{1,2}-\d{1,2}-\d{4}\.pdf$/, ""),
      this.leftMargin,
      updatedY,
    );
    updatedY += 0.4;

    this.doc.setFontSize(10);
    this.doc.setFont("helvetica", "normal");
    const createdDate = t(
      "pdfGenerator.createdDate",
      `Created ${formatISOTimeToReadableDateTime(new Date().toISOString(), timezone)}`,
      {
        createdDate: formatISOTimeToReadableDateTime(
          new Date().toISOString(),
          timezone,
        ),
      },
    );
    this.doc.text(createdDate, this.leftMargin, updatedY);

    const rightX = this.rightMargin - 4.0;
    let rightY = updatedY - 0.3;
    const fallbackText = t("pdfGenerator.noData", "No data available");

    rightY += 0.3;
    this.doc.setFont("helvetica", "bold");
    this.doc.text(t("pdfGenerator.data", "Data"), rightX, rightY);
    this.doc.setFont("helvetica", "normal");
    const formattedDataTs =
      dataUpdatedTimestamp === fallbackText
        ? fallbackText
        : formatISOTimeToReadableDateTime(dataUpdatedTimestamp, timezone);
    this.doc.text(
      t("pdfGenerator.updated", `Updated ${formattedDataTs}`, {
        updatedDate: formattedDataTs,
      }),
      rightX + 0.7,
      rightY,
    );

    rightY += 0.2;
    this.doc.setFont("helvetica", "bold");
    this.doc.text(t("pdfGenerator.analysis", "Analysis"), rightX, rightY);
    this.doc.setFont("helvetica", "normal");
    const formattedAnalysisTs =
      analysisPerformedTimestamp === fallbackText
        ? fallbackText
        : formatISOTimeToReadableDateTime(analysisPerformedTimestamp, timezone);
    this.doc.text(
      t("pdfGenerator.performed", `Performed ${formattedAnalysisTs}`, {
        performedDate: formattedAnalysisTs,
      }),
      rightX + 0.7,
      rightY,
    );

    rightY += 0.2;
    this.doc.setFont("helvetica", "bold");
    this.doc.text(t("pdfGenerator.skus", "SKUs"), rightX, rightY);
    this.doc.setFont("helvetica", "normal");
    this.doc.text(`${printedSkuCount}`, rightX + 0.7, rightY);

    const taskSectionPadding = 0.2;
    const boxTopY = rightY + taskSectionPadding;
    const innerPadding = 0.1;
    const descriptionFontSize = 10;
    const lineHeightFactor = 1.15;
    const descriptionLineHeight = (descriptionFontSize / 72) * lineHeightFactor;

    this.doc.setFontSize(descriptionFontSize);
    const descriptionText = t(
      "pdfGenerator.taskDescription",
      "Create new Generate Move tasks to move the included SKUs from Reserve Storage locations to the Forward Pick ASRS.",
    );
    const availableTextWidth =
      this.rightMargin - this.leftMargin - innerPadding * 2;
    const descriptionLines = this.doc.splitTextToSize(
      descriptionText,
      availableTextWidth,
    );
    const descriptionHeight = descriptionLines.length * descriptionLineHeight;

    const blackBarHeight = 0.3;
    const totalBoxHeight =
      blackBarHeight + descriptionHeight + innerPadding * 2;

    this.doc.setFillColor(0, 0, 0);
    this.doc.rect(
      this.leftMargin,
      boxTopY,
      this.rightMargin - this.leftMargin,
      blackBarHeight,
      "F",
    );

    this.doc.setLineWidth(0.02);
    this.doc.setDrawColor(0, 0, 0);
    this.doc.rect(
      this.leftMargin,
      boxTopY,
      this.rightMargin - this.leftMargin,
      totalBoxHeight,
      "S",
    );

    this.doc.setFontSize(12);
    this.doc.setFont("helvetica", "bold");
    this.doc.setTextColor(255, 255, 255);
    const taskTextY = boxTopY + blackBarHeight / 2 + 0.04;
    this.doc.text(
      t("pdfGenerator.taskLabel", "Task: Generate Move to ASRS"),
      this.leftMargin + innerPadding,
      taskTextY,
    );

    this.doc.setTextColor(0, 0, 0);
    this.doc.setFont("helvetica", "normal");
    this.doc.setFontSize(descriptionFontSize);
    const descriptionAreaTopY = boxTopY + blackBarHeight + innerPadding;
    const descriptionAreaBottomY = boxTopY + totalBoxHeight - innerPadding;
    const availableHeight = descriptionAreaBottomY - descriptionAreaTopY;

    const extraTopPadding = 0.1;
    const centeredY =
      descriptionAreaTopY + availableHeight / 2 - descriptionHeight / 2;
    const newDescriptionY = centeredY + extraTopPadding;

    this.doc.text(
      descriptionLines,
      this.leftMargin + innerPadding,
      newDescriptionY,
    );

    const sectionBottomPadding = 0.4;
    updatedY = boxTopY + totalBoxHeight + sectionBottomPadding;

    this.doc.setFontSize(10);
    this.doc.setFont("helvetica", "normal");
    this.doc.setLineWidth(0.01);
    this.doc.setTextColor(0, 0, 0);
    return updatedY;
  }

  public addPageFooter(
    doc: jsPDF,
    pageNumber: number,
    totalPages: string | number,
    leftMargin: number,
    rightMargin: number,
    bottomMargin: number,
    t: TFunction,
  ) {
    if (totalPages) {
      const footerTextY = bottomMargin + 0.1;
      doc.setFontSize(10);
      doc.setFont("helvetica", "normal");
      const pageText = t(
        "pdfGenerator.pageOfTotal",
        `Page ${pageNumber} of ${totalPages}`,
        { pageNumber, totalPages },
      );
      const textWidth = doc.getTextWidth(pageText);
      doc.text(
        pageText,
        (rightMargin + leftMargin - textWidth) / 2 + leftMargin,
        footerTextY,
      );
    }
  }
}
