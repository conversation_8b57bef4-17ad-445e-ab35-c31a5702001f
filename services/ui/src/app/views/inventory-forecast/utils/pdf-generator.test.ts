import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";
import { jsPDF } from "jspdf";
import { TFunction } from "i18next";
import {
  InventoryForecastPDFGenerator,
  PDFGeneratorConfig,
} from "./pdf-generator";
import type { InventoryForecastItem, SkuLocationDetail } from "../types";
import { logger } from "../../../utils/logger";

vi.mock("jspdf", () => {
  const mockMethods = {
    text: vi.fn(),
    addImage: vi.fn(),
    line: vi.fn(),
    rect: vi.fn(),
    save: vi.fn(),
    addPage: vi.fn(),
    setPage: vi.fn(),
    setFont: vi.fn(),
    setFontSize: vi.fn(),
    setTextColor: vi.fn(),
    setFillColor: vi.fn(),
    setDrawColor: vi.fn(),
    setLineWidth: vi.fn(),
    roundedRect: vi.fn(),
    splitTextToSize: vi.fn((text: string) => text.split("\n")),
    getNumberOfPages: vi.fn(() => 1),
    getTextWidth: vi.fn(() => 1),
  };
  return {
    jsPDF: vi.fn(() => mockMethods),
  };
});

vi.mock("../../../utils/logger", () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
}));

vi.mock("../../../utils/date-util", () => ({
  formatISOTimeToReadableDateTime: vi.fn(
    (isoString, timezone = "UTC") => `Formatted ${isoString} in ${timezone}`,
  ),
}));

const mockT: TFunction = vi.fn(
  (key: string | string[], defaultValueOrOptions?: any, options?: any) => {
    const actualKey = Array.isArray(key) ? key[0] : key;
    let actualOptions: any = {};
    let fallbackString: string | undefined;

    if (
      typeof defaultValueOrOptions === "object" &&
      defaultValueOrOptions !== null
    ) {
      actualOptions = defaultValueOrOptions;
    } else if (typeof defaultValueOrOptions === "string") {
      fallbackString = defaultValueOrOptions;
      if (typeof options === "object" && options !== null) {
        actualOptions = options;
      }
    }

    const pageNumber = actualOptions?.pageNumber ?? "X";
    const totalPages = actualOptions?.totalPages ?? "Y";
    const quantity = actualOptions?.quantity ?? 0;
    const timestamp = actualOptions?.timestamp;
    const date = actualOptions?.date;
    const index = actualOptions?.index ?? 0;
    const total = actualOptions?.total ?? 0;

    switch (actualKey) {
      case "pdfGenerator.noData":
        return "No data available";
      case "pdfGenerator.createdDate":
        return date
          ? `Created ${date}`
          : `Created Formatted ${new Date().toISOString()} in UTC`;
      case "pdfGenerator.updated":
        return timestamp
          ? `Updated ${timestamp}`
          : (fallbackString ?? `Updated Formatted timestamp_data in UTC`);
      case "pdfGenerator.performed":
        return timestamp
          ? `Performed ${timestamp}`
          : (fallbackString ?? `Performed Formatted timestamp_analysis in UTC`);
      case "pdfGenerator.taskDescription":
        return "Mock Task Description";
      case "pdfGenerator.taskLabel":
        return "Mock Task Label";
      case "pdfGenerator.page":
        return `Page ${pageNumber} of ${totalPages}`;
      case "pdfGenerator.eaches":
        return `${quantity} eaches`;
      case "pdfGenerator.noReserveLocationsError":
        return "No locations found under Reserve Storage for the selected SKU(s). PDF cannot be generated.";
      case "pdfGenerator.skuOfTotal":
        return `SKU ${index} of ${total}`;
      case "pdfGenerator.forecastLabel":
        return `Forecast Forward Pick Tomorrow`;
      case "pdfGenerator.locationId":
        return "Location ID";
      case "pdfGenerator.containers":
        return "Containers";
      case "pdfGenerator.skuQuantity":
        return "SKU Quantity";
      case "pdfGenerator.caseQuantity":
        return "Case Quantity";
      case "pdfGenerator.casesRequired":
        return "Cases Required";
      case "pdfGenerator.conditionCode":
        return "Condition Code";
      case "pdfGenerator.data":
        return "Data";
      case "pdfGenerator.analysis":
        return "Analysis";
      case "pdfGenerator.skus":
        return "SKUs";
      case "pdfGenerator.confidential":
        return "CONFIDENTIAL";

      default:
        return fallbackString ?? actualKey;
    }
  },
) as unknown as TFunction;

describe("InventoryForecastPDFGenerator", () => {
  let generator: InventoryForecastPDFGenerator;
  let mockConfig: PDFGeneratorConfig;
  let mockJsPDFInstance: any;

  const mockForecastBase = {
    twoDayForwardPick: null,
    twoDayDemand: null,
    forwardPickTomorrow: null,
    knownDemand: null,
    demandTomorrow: null,
    averageDemand: 0,
    averageReplenishment: 0,
  };

  const mockProjectedBase = {
    projectedForwardPick: 0,
    allocatedOrders: 0,
    pendingPicks: 0,
    pendingReplenishment: 0,
  };
  const mockCurrentBase = {
    forwardPick: null,
    reserveStorage: null,
    total: null,
  };

  const mockSku1: InventoryForecastItem = {
    sku: "SKU001",
    forecast: { ...mockForecastBase, forwardPickTomorrow: 50 },
    projected: { ...mockProjectedBase },
    current: { ...mockCurrentBase, reserveStorage: 100, forwardPick: 10 },
  };
  const mockSku2: InventoryForecastItem = {
    sku: "SKU002",
    forecast: { ...mockForecastBase, forwardPickTomorrow: 30 },
    projected: { ...mockProjectedBase },
    current: { ...mockCurrentBase, reserveStorage: 50, forwardPick: 5 },
  };
  const mockSku3_NoLocation: InventoryForecastItem = {
    sku: "SKU003",
    forecast: { ...mockForecastBase, forwardPickTomorrow: 10 },
    projected: { ...mockProjectedBase },
    current: { ...mockCurrentBase, reserveStorage: 20, forwardPick: 2 },
  };

  const mockLocationDetailsSku1: SkuLocationDetail[] = [
    {
      locationId: "LOC-A1",
      quantity: 40,
      containerQuantity: 10,
      containerCount: 4,
      conditionCode: "GOOD",
    },
    {
      locationId: "LOC-A2",
      quantity: 60,
      containerQuantity: 20,
      containerCount: 3,
      conditionCode: "GOOD",
    },
  ];
  const mockLocationDetailsSku2: SkuLocationDetail[] = [
    {
      locationId: "LOC-B1",
      quantity: 50,
      containerQuantity: 5,
      containerCount: 10,
      conditionCode: "GOOD",
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    generator = new InventoryForecastPDFGenerator();
    mockJsPDFInstance = (jsPDF as any).mock.results[0].value;

    mockConfig = {
      selectedSkus: [mockSku1, mockSku2],
      skusPerPage: 5,
      enteredFileName: "TestPickList",
      dataUpdateTimestamp: "timestamp_data",
      analysisPerformedTimestamp: "timestamp_analysis",
      getSkuLocationDetails: vi.fn(async (sku: string) => {
        if (sku === "SKU001") return mockLocationDetailsSku1;
        if (sku === "SKU002") return mockLocationDetailsSku2;
        if (sku === "SKU003") return [];
        return [];
      }),
      onComplete: vi.fn(),
      t: mockT,
      timezone: "UTC",
    };

    vi.mocked(mockJsPDFInstance.getNumberOfPages).mockReturnValue(1);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it("should create a jsPDF instance on initialization", () => {
    expect(jsPDF).toHaveBeenCalledTimes(1);
    expect(jsPDF).toHaveBeenCalledWith({
      orientation: "portrait",
      unit: "in",
      format: [8.5, 11],
    });
  });

  it("should generate PDF with correct header and footer", async () => {
    await generator.generatePDF(mockConfig);

    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      expect.stringContaining(mockConfig.enteredFileName),
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      expect.stringContaining("Created"),
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      expect.stringContaining("Data"),
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      expect.stringContaining("Updated Formatted timestamp_data"),
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      expect.stringContaining("Analysis"),
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      expect.stringContaining("Performed Formatted timestamp_analysis"),
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      expect.stringContaining("SKUs"),
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      `${mockConfig.selectedSkus.length}`,
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      expect.stringContaining("Mock Task Label"),
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      ["Mock Task Description"],
      expect.any(Number),
      expect.any(Number),
    );

    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      expect.stringContaining("Page 1 of 1"),
      expect.any(Number),
      expect.any(Number),
    );
  });

  it("should filter out SKUs with no location details", async () => {
    mockConfig.selectedSkus = [mockSku1, mockSku3_NoLocation, mockSku2];
    await generator.generatePDF(mockConfig);

    expect(mockConfig.getSkuLocationDetails).toHaveBeenCalledWith("SKU001");
    expect(mockConfig.getSkuLocationDetails).toHaveBeenCalledWith("SKU003");
    expect(mockConfig.getSkuLocationDetails).toHaveBeenCalledWith("SKU002");

    expect(logger.info).toHaveBeenCalledWith(
      `Skipping SKU ${mockSku3_NoLocation.sku} as it has no Reserve Storage details.`,
    );

    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      mockSku1.sku,
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      mockSku2.sku,
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).not.toHaveBeenCalledWith(
      mockSku3_NoLocation.sku,
      expect.any(Number),
      expect.any(Number),
    );

    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      "2",
      expect.any(Number),
      expect.any(Number),
    );

    expect(mockConfig.onComplete).toHaveBeenCalledTimes(1);
    expect(mockJsPDFInstance.save).toHaveBeenCalledTimes(1);
  });

  it("should throw an error if no selected SKUs have reserve locations", async () => {
    mockConfig.selectedSkus = [mockSku3_NoLocation];

    await expect(generator.generatePDF(mockConfig)).rejects.toThrow(
      "No locations found under Reserve Storage for the selected SKU(s). PDF cannot be generated.",
    );

    expect(logger.info).toHaveBeenCalledWith(
      `Skipping SKU ${mockSku3_NoLocation.sku} as it has no Reserve Storage details.`,
    );
    expect(mockConfig.onComplete).not.toHaveBeenCalled();
    expect(mockJsPDFInstance.save).not.toHaveBeenCalled();
    expect(logger.error).toHaveBeenCalledWith(
      expect.stringContaining("Failed to generate PDF"),
    );
  });

  it("should calculate 'Cases Required' correctly", async () => {
    await generator.generatePDF(mockConfig);

    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      "4",
      5.2,
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      "1",
      5.2,
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      "6",
      5.2,
      expect.any(Number),
    );
  });

  it("should handle zero forecast quantity correctly", async () => {
    mockConfig.selectedSkus = [
      {
        ...mockSku1,
        forecast: { ...mockForecastBase, forwardPickTomorrow: 0 },
      },
    ];
    mockConfig.getSkuLocationDetails = vi
      .fn()
      .mockResolvedValue(mockLocationDetailsSku1);

    await generator.generatePDF(mockConfig);

    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      "0",
      5.2,
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      "0",
      5.2,
      expect.any(Number),
    );
  });

  it("should handle zero case quantity correctly", async () => {
    const locationWithZeroCaseQty: SkuLocationDetail[] = [
      {
        locationId: "LOC-C1",
        quantity: 50,
        containerQuantity: 0,
        containerCount: 5,
        conditionCode: "GOOD",
      },
      {
        locationId: "LOC-C2",
        quantity: 30,
        containerQuantity: 10,
        containerCount: 3,
        conditionCode: "GOOD",
      },
    ];
    mockConfig.selectedSkus = [
      {
        ...mockSku1,
        forecast: { ...mockForecastBase, forwardPickTomorrow: 25 },
      },
    ];
    mockConfig.getSkuLocationDetails = vi
      .fn()
      .mockResolvedValue(locationWithZeroCaseQty);

    await generator.generatePDF(mockConfig);

    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      "0",
      5.2,
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      "3",
      5.2,
      expect.any(Number),
    );
  });

  it("should handle page breaks correctly when skusPerPage = 1", async () => {
    mockConfig.skusPerPage = 1;
    mockConfig.selectedSkus = [mockSku1, mockSku2];
    vi.mocked(mockJsPDFInstance.getNumberOfPages).mockReset();
    vi.mocked(mockJsPDFInstance.getNumberOfPages).mockReturnValue(2);

    await generator.generatePDF(mockConfig);

    expect(mockJsPDFInstance.addPage).toHaveBeenCalledTimes(1);

    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      expect.stringContaining(mockConfig.enteredFileName),
      expect.any(Number),
      expect.any(Number),
    );
    const titleCalls = mockJsPDFInstance.text.mock.calls.filter(
      (call: any[]) =>
        typeof call[0] === "string" &&
        call[0].includes(mockConfig.enteredFileName),
    );
    expect(titleCalls.length).toBe(2);

    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      expect.stringContaining("Page 1 of 2"),
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      expect.stringContaining("Page 2 of 2"),
      expect.any(Number),
      expect.any(Number),
    );
    const footerPageCalls = mockJsPDFInstance.text.mock.calls.filter(
      (call: any[]) =>
        typeof call[0] === "string" && /Page \d+ of \d+/.test(call[0]),
    );
    expect(footerPageCalls.length).toBe(2);
  });

  it("should attempt page break based on height when skusPerPage > 1 (conceptual test)", async () => {
    mockConfig.skusPerPage = 2;
    mockConfig.selectedSkus = [mockSku1, mockSku2];

    await generator.generatePDF(mockConfig);

    expect(mockJsPDFInstance.addPage).not.toHaveBeenCalled();
  });

  it("should use fallback filename if enteredFileName is empty", async () => {
    vi.useFakeTimers();
    const date = new Date(2025, 3, 16);
    vi.setSystemTime(date);

    mockConfig.enteredFileName = "";
    const expectedFallbackName = `Pick_List_${new Date().toLocaleDateString().replace(/\//g, "-")}.pdf`;

    await generator.generatePDF(mockConfig);

    expect(mockJsPDFInstance.save).toHaveBeenCalledWith(expectedFallbackName);

    vi.useRealTimers();
  });

  it("should use fallback text for timestamps if not provided", async () => {
    mockConfig.dataUpdateTimestamp = undefined;
    mockConfig.analysisPerformedTimestamp = undefined;

    await generator.generatePDF(mockConfig);

    expect(mockT).toHaveBeenCalledWith(
      "pdfGenerator.updated",
      "Updated No data available",
      { updatedDate: "No data available" },
    );
    expect(mockT).toHaveBeenCalledWith(
      "pdfGenerator.performed",
      "Performed No data available",
      { performedDate: "No data available" },
    );

    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      "Updated No data available",
      expect.any(Number),
      expect.any(Number),
    );
    expect(mockJsPDFInstance.text).toHaveBeenCalledWith(
      "Performed No data available",
      expect.any(Number),
      expect.any(Number),
    );
  });

  it("should call onComplete and save upon successful generation", async () => {
    await generator.generatePDF(mockConfig);

    expect(mockConfig.onComplete).toHaveBeenCalledTimes(1);
    expect(mockJsPDFInstance.save).toHaveBeenCalledTimes(1);
    expect(mockJsPDFInstance.save).toHaveBeenCalledWith(
      expect.stringContaining("TestPickList"),
    );
    expect(logger.error).not.toHaveBeenCalled();
  });

  it("should log error and re-throw if getSkuLocationDetails fails", async () => {
    const error = new Error("Failed to fetch details");
    mockConfig.getSkuLocationDetails = vi.fn().mockRejectedValue(error);

    await expect(generator.generatePDF(mockConfig)).rejects.toThrow(
      "Failed to fetch details",
    );

    expect(logger.error).toHaveBeenCalledWith(
      `Failed to generate PDF: ${error}`,
    );
    expect(mockConfig.onComplete).not.toHaveBeenCalled();
    expect(mockJsPDFInstance.save).not.toHaveBeenCalled();
  });

  it("should log error and re-throw if jsPDF save fails", async () => {
    const saveError = new Error("PDF save failed");
    vi.mocked(mockJsPDFInstance.save).mockImplementation(() => {
      throw saveError;
    });

    await expect(generator.generatePDF(mockConfig)).rejects.toThrow(
      "PDF save failed",
    );

    expect(logger.error).toHaveBeenCalledWith(
      `Failed to generate PDF: ${saveError}`,
    );
    expect(mockConfig.onComplete).not.toHaveBeenCalled();
  });
});
