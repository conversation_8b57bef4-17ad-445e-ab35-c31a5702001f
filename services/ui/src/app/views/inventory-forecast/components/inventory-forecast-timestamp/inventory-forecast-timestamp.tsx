import { useEffect, useState } from "react";
import { useConfigSetting } from "../../../../config/hooks/use-config";
import { formatISOTimeToReadableDateTime } from "../../../../utils/date-util";
import { useTranslation } from "react-i18next";
import styles from "./inventory-forecast-timestamp.module.css";

interface InventoryForecastTimestampProps {
  isLoading: boolean;
  error: unknown;
  inventoryUpdatedAt?: string;
  forecastPerformedAt?: string;
}

export const InventoryForecastTimestamp = ({
  isLoading,
  error,
  inventoryUpdatedAt,
  forecastPerformedAt,
}: InventoryForecastTimestampProps) => {
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const timezone = timezoneConfig?.value as string;
  const { t } = useTranslation();

  const [formattedData, setFormattedData] = useState<{
    dataUpdateTimestamp?: string;
    analysisPerformedTimestamp?: string;
  }>({});

  useEffect(() => {
    setFormattedData({
      dataUpdateTimestamp: inventoryUpdatedAt
        ? formatISOTimeToReadableDateTime(inventoryUpdatedAt, timezone)
        : undefined,
      analysisPerformedTimestamp: forecastPerformedAt
        ? formatISOTimeToReadableDateTime(forecastPerformedAt, timezone)
        : undefined,
    });
  }, [inventoryUpdatedAt, forecastPerformedAt, timezone]);

  if (isLoading) {
    return (
      <div>
        {t(
          "inventoryForecastTimestamp.loadingTimestampData",
          "Loading timestamp data...",
        )}
      </div>
    );
  }

  if (error) {
    return (
      <div>
        {t("inventoryForecastTimestamp.noDataAvailable", "No data available")}
      </div>
    );
  }

  return (
    <div className={styles.timestampContainer}>
      <div
        data-testid="ict-inventory-forecast-data-updated-timestamp"
        style={{ marginBottom: "8px" }}
      >
        {formattedData.dataUpdateTimestamp
          ? t(
              "inventoryForecastTimestamp.inventoryDataUpdated",
              "Inventory Data Updated: {{dataUpdateTimestamp}}",
              {
                dataUpdateTimestamp: formattedData.dataUpdateTimestamp,
              },
            )
          : t(
              "inventoryForecastTimestamp.noDataAvailable",
              "No data available",
            )}
      </div>
      <div data-testid="ict-inventory-forecast-data-sku-forecast-timestamp">
        {formattedData.analysisPerformedTimestamp
          ? t(
              "inventoryForecastTimestamp.skuForecastPerformed",
              "SKU Forecast Performed: {{analysisPerformedTimestamp}}",
              {
                analysisPerformedTimestamp:
                  formattedData.analysisPerformedTimestamp,
              },
            )
          : t(
              "inventoryForecastTimestamp.noDataAvailable",
              "No data available",
            )}
      </div>
    </div>
  );
};
