import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { useCallback, useMemo } from "react";
import { Datagrid } from "../../../../components/datagrid";
import type { FilterState } from "../../../../components/datagrid/types";
import type { WorkstationStatusOrdersListItem } from "../../types";
import styles from "./workstation-order-status-table.module.css";
import { useTranslation } from "react-i18next";
import { Link } from "@carbon/react";
import { WarningAltFilled, InProgress, Pending } from "@carbon/icons-react";
import { useNavigate } from "react-router";

interface WorkstationOrderStatusTableProps {
  data: WorkstationStatusOrdersListItem[];
  pagination: PaginationState;
  setPagination: (pagination: PaginationState) => void;
  sorting: SortingState;
  setSorting: (sorting: SortingState) => void;
  columnFilters: ColumnFiltersState;
  setColumnFilters: (filters: ColumnFiltersState) => void;
  isLoading: boolean;
  isFetching: boolean;
  error: Error | null;
  rowCount: number;
  onRefresh?: () => void;
  setGlobalFilter: (globalFilter: string) => void;
  onExport?: () => void;
}

export function WorkstationOrderStatusTable({
  data,
  pagination,
  setPagination,
  sorting,
  setSorting,
  columnFilters: _columnFilters,
  setColumnFilters,
  isLoading,
  isFetching,
  error,
  rowCount,
  onRefresh,
  setGlobalFilter,
  onExport,
}: WorkstationOrderStatusTableProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  // Define columns using createColumnHelper
  const columnHelper = useMemo(
    () => createColumnHelper<WorkstationStatusOrdersListItem>(),
    [],
  );

  // Function to render status icon based on order status
  const renderStatusIcon = useCallback((status: string) => {
    const normalizedStatus = status.toLowerCase();

    if (normalizedStatus.includes("delayed")) {
      return (
        <WarningAltFilled className={styles.statusIconDelayed} size={16} />
      );
    } else if (normalizedStatus.includes("active")) {
      return <InProgress className={styles.statusIconActive} size={16} />;
    } else if (normalizedStatus.includes("assigned")) {
      return <Pending className={styles.statusIconAssigned} size={16} />;
    }
    return null;
  }, []);

  const columns = useMemo(
    () => [
      columnHelper.accessor("station", {
        header: t("workstationOrderStatus.station", "Station"),
        size: 120,
      }),
      columnHelper.accessor("position", {
        header: t("workstationOrderStatus.position", "Position"),
        size: 120,
      }),
      columnHelper.accessor("dwellTime", {
        header: t("workstationOrderStatus.dwellTime", "Dwell Time"),
        size: 120,
        cell: (info) => {
          const dwellTime = info.getValue();
          const row = info.row.original;
          const isExceeded = row.orderStatus.toLowerCase().includes("delayed");

          return (
            <span className={isExceeded ? styles.dwellTimeExceeded : undefined}>
              {dwellTime} min
            </span>
          );
        },
      }),
      columnHelper.accessor("orderStatus", {
        header: t("workstationOrderStatus.orderStatus", "Order Status"),
        size: 140,
        cell: (info) => (
          <div className={styles.statusContainer}>
            {renderStatusIcon(info.getValue())}
            {info.getValue()}
          </div>
        ),
      }),
      columnHelper.accessor("pickTask", {
        header: t("workstationOrderStatus.pickTask", "Pick Task"),
        size: 140,
      }),
      columnHelper.accessor("orderId", {
        header: t("workstationOrderStatus.orderId", "Order ID"),
        size: 140,
        cell: (info) => (
          <Link
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/ict-workstation-active-orders/${info.getValue()}`);
            }}
            onMouseDown={(e) => e.stopPropagation()}
            onTouchStart={(e) => e.stopPropagation()}
            style={{ cursor: "pointer" }}
          >
            {info.getValue()}
          </Link>
        ),
      }),
      columnHelper.accessor("container", {
        header: t("workstationOrderStatus.container", "Container"),
        size: 140,
      }),
      columnHelper.accessor("completedPicks", {
        header: t("workstationOrderStatus.completedPicks", "Completed Picks"),
        size: 140,
      }),
      columnHelper.accessor("totalPicks", {
        header: t("workstationOrderStatus.totalPicks", "Total Picks"),
        size: 140,
      }),
      columnHelper.accessor("arrivalTime", {
        header: t("workstationOrderStatus.arrivalTime", "Arrival Time"),
        size: 180,
        cell: (info) => {
          const value = info.getValue();
          return value ? new Date(value).toLocaleString() : "";
        },
      }),
    ],
    [t, navigate, renderStatusIcon, columnHelper],
  );

  // Handlers for Datagrid callbacks
  const handlePageChange = useCallback(
    (newPagination: PaginationState) => {
      setPagination(newPagination);
    },
    [setPagination],
  );

  const handleSort = useCallback(
    (newSorting: SortingState) => {
      setSorting(newSorting);
    },
    [setSorting],
  );

  const handleFilter = useCallback(
    (newFilters: FilterState) => {
      // Convert FilterState to ColumnFiltersState
      const newColumnFilters: ColumnFiltersState = Object.entries(
        newFilters.filters,
      ).map(([id, value]) => ({
        id,
        value,
      }));
      setColumnFilters(newColumnFilters);
      setGlobalFilter(newFilters.globalFilter);
    },
    [setColumnFilters, setGlobalFilter],
  );

  const handleExport = useCallback(() => {
    if (onExport) {
      onExport();
    }
  }, [onExport]);

  return (
    <div className={styles.tableContainer}>
      <div className={styles.tableWrapper}>
        <div className={styles.tableTitle}>
          {t("workstationOrderStatus.activeOrders", "Active Orders")}
        </div>
        <div className={styles.datagridWrapper}>
          <Datagrid
            columns={columns}
            data={data}
            mode="server"
            totalRows={rowCount}
            isLoading={isLoading || isFetching}
            error={error ? error.message : undefined}
            onPageChange={handlePageChange}
            onSort={handleSort}
            onFilter={handleFilter}
            onExport={handleExport}
            onRefreshClick={onRefresh}
            showRefreshButton={!!onRefresh}
            initialPagination={pagination}
            initialSorting={sorting}
            className={styles.datagrid}
          />
        </div>
      </div>
    </div>
  );
}
