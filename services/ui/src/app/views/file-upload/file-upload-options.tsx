import { Analytics } from "@carbon/icons-react";
import { Toggle } from "@carbon/react";
import { OptionsAccordionGroup } from "../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../components/options/options-accordion/options-accordion";
import { OptionsContainer } from "../../components/options/options-container/options-container";
import type { BaseViewOptionsProps } from "../view-registry.types";
import styles from "./file-upload-options.module.css";

export type FileUploadOptions = {
  sapOrderManagement: boolean;
  sapOrderDetails: boolean;
};

interface FileUploadOptionsProps
  extends BaseViewOptionsProps<FileUploadOptions> {
  options: FileUploadOptions;
}

export const FileUploadOptions = ({
  options,
  onChange,
  onClose,
  onSave,
}: FileUploadOptionsProps) => (
  <OptionsContainer onClose={onClose} onSave={onSave}>
    <OptionsAccordion>
      <OptionsAccordionGroup
        id="display"
        title="Display"
        icon={<Analytics size={20} />}
      >
        <Toggle
          id="sap-order-management-toggle"
          labelText="SAP Order Management"
          toggled={options.sapOrderManagement}
          onToggle={(checked) =>
            onChange({ ...options, sapOrderManagement: checked })
          }
        />
        <p className={styles.toggleDescription}>
          Show upload for SAP Order Management
        </p>

        <Toggle
          id="sap-order-details-toggle"
          labelText="SAP Order Details"
          toggled={options.sapOrderDetails}
          onToggle={(checked) =>
            onChange({ ...options, sapOrderDetails: checked })
          }
        />
        <p className={styles.toggleDescription}>
          Show upload for SAP Order Details
        </p>
      </OptionsAccordionGroup>
    </OptionsAccordion>
  </OptionsContainer>
);
