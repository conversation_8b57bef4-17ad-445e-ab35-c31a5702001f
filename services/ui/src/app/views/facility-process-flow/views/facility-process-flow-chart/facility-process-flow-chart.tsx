import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import React<PERSON>low, {
  Edge,
  Node,
  useEdgesState,
  useNodesState,
  OnMove,
  SelectionMode,
} from "reactflow";
import "reactflow/dist/style.css";
import IctFloatingEdge, { FloatingEdgePropsData } from "./edges/floating-edge";
import IctClickableTransparentEdge from "./edges/clickable-transparent-edge";
import CustomControls from "./components/custom-controls";
import styles from "./../../../../components/datagrid/datagrid.module.scss";
import IctNode from "./nodes/ict-node";
import IctAisleNode from "./nodes/aisle-node";
import { Logger } from "../../../../utils";
import { useRoles } from "../../../../auth/hooks/use-roles";
import "./facility-process-flow-chart.css";
import {
  useConfigSetting,
  useFeatureFlag,
} from "../../../../config/hooks/use-config";
import { ictApi } from "../../../../api/ict-api";
import {
  GraphEdge,
  Area,
  Metric,
} from "../../types/facility-process-flow-types";
import { resolveNodeName } from "../../locale/process-flow-locale-util";
import { LoadingView } from "../../../../components/loading-view/loading-view";
import {
  EDGE_COLOR_DARK,
  EDGE_COLOR_LIGHT,
  EDGE_STROKE_WIDTH,
} from "./constants";
import { ThemeMode, useTheme } from "../../../../layout/theme";
import { getAutoLayoutedElements } from "../../utils";
import { toastManager } from "../../../../components/toast/toast-container";
import { useApiErrorState } from "../../../../hooks/use-api-error-state";
import { WidgetNotification } from "../../../../components/widget-container/widget-notification";
import { useTranslation } from "react-i18next";

const logger = new Logger("IctFacilityProcessFlow");

const edgeTypes = {
  "floating-edge": IctFloatingEdge,
  "clickable-transparent-edge": IctClickableTransparentEdge,
};

const nodeTypes = {
  "ict-node": IctNode,
  "ict-aisle-node": IctAisleNode,
};

interface NodeOptions {
  metrics: string[];
  hasChildren: boolean;
}

interface NodeConfig {
  [key: string]: NodeOptions;
}
interface GraphConfig {
  [key: string]: NodeConfig;
}

export interface ProcessFlowGraphProps {
  isInteractive: boolean;
  parentNodeId?: string;
  onEdgeSelect: (
    edgeId: string,
    edgeLabel: string,
    isBiDirectional: boolean,
  ) => void;
  onNodeSelect: (nodeId: string, nodeLabel: string) => void;
  onClearSelection: () => void;
  setIsInteractive: Dispatch<SetStateAction<boolean>>;
  setLastProcessedTime: Dispatch<SetStateAction<string>>;
}

function FacilityProcessFlowChart({
  isInteractive = false,
  parentNodeId = "",
  onEdgeSelect,
  onNodeSelect,
  onClearSelection,
  setIsInteractive,
  setLastProcessedTime,
}: ProcessFlowGraphProps) {
  const { t } = useTranslation();
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const reactFlowRef = useRef<HTMLInputElement>(null);
  const [selectedEdgeId, setSelectedEdgeId] = useState<string>("");
  const { hasConfiguratorAccess } = useRoles();
  // Configuration settings
  const { enabled: edgeAnimationEffectEnabled } = useFeatureFlag(
    "ict-facility-process-flow-edge-animation-effect",
  );
  const { enabled: edgeLabelsEnabled } = useFeatureFlag(
    "ict-facility-process-flow-edge-labels",
  );
  const { enabled: edgeLabelAlertsEnabled } = useFeatureFlag(
    "ict-facility-process-flow-edge-label-alerts",
  );
  const { enabled: edgeLabelStatusIndicatorsEnabled } = useFeatureFlag(
    "ict-facility-process-flow-edge-label-status-indicators",
  );
  const { enabled: nodeAlertsEnabled } = useFeatureFlag(
    "ict-facility-process-flow-node-alerts",
  );
  const { enabled: nodeDrilldownButtonEnabled } = useFeatureFlag(
    "ict-facility-process-flow-node-drilldown-button",
  );
  const { enabled: nodeMetricsEnabled } = useFeatureFlag(
    "ict-facility-process-flow-node-metrics",
  );
  const { enabled: pollingEnabled } = useFeatureFlag(
    "ict-facility-process-flow-polling",
  );
  const { setting: pollingInterval } = useConfigSetting(
    "ict-facility-process-flow-polling-interval",
  );
  const { setting: processFlowGraphConfigSetting } = useConfigSetting(
    "ict-facility-process-flow-graph-config",
  );

  // Setting theme for edges
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;

  const edgeStyle = {
    strokeWidth: EDGE_STROKE_WIDTH,
    stroke: isDark ? EDGE_COLOR_DARK : EDGE_COLOR_LIGHT,
  };

  const processFlowGraphConfig =
    processFlowGraphConfigSetting?.value as GraphConfig;

  // Data fetching/updating
  // API call to feed the chart with process flow data
  const {
    data: processFlowData,
    isRefetchError,
    isLoading,
    error,
  } = ictApi.client.useQuery(
    "get",
    parentNodeId
      ? "/inventory/process-flow/area/{areaId}"
      : "/inventory/process-flow/areas",
    {
      params: {
        path: { areaId: parentNodeId },
      },
    },
    {
      enabled: parentNodeId !== undefined,
      refetchInterval:
        pollingEnabled && pollingInterval
          ? (pollingInterval.value as number)
          : false,
    },
  );

  const isNoDataAvailable = useApiErrorState(error);

  // Watch for polling errors and show toast when they occur
  useEffect(() => {
    if (isRefetchError) {
      toastManager.addToast({
        id: `process-flow-polling-error-${Date.now()}`,
        title: t(
          "facilityProcessFlowDataStalenessIndicator.pollingErrorTitle",
          "Data Refresh Failed",
        ),
        message: t(
          "facilityProcessFlowDataStalenessIndicator.pollingErrorMessage",
          "The last data refresh failed. Showing the most recent available data.",
        ),
        type: "warning",
        duration: 5000,
      });
    }
  }, [isRefetchError, t]);

  /**
   * Sets the nodes and edges whenever the processFlowData from the API changes
   */
  useEffect(() => {
    if (processFlowData) {
      if (
        processFlowData.areas === undefined ||
        processFlowData.edges === undefined
      ) {
        logger.error("Invalid data received, areas and edges are required.");
        return;
      }

      if (processFlowData.lastProcessedTime) {
        if (Number.isNaN(Number(processFlowData.lastProcessedTime))) {
          setLastProcessedTime(processFlowData.lastProcessedTime);
        } else {
          const utcDate = new Date(
            parseFloat(processFlowData.lastProcessedTime) * 1000,
          );
          setLastProcessedTime(utcDate.toISOString());
        }
      }

      let reactFlowNodes = mapNodesToReactFlowNodes(
        parentNodeId,
        processFlowData.areas,
      );
      let reactFlowEdges = mapEdgesToReactFlowEdges(processFlowData.edges);

      // if any nodes have the same position, use the automatic layout to prevent overlaps from inital node creation
      const valueArr = processFlowData.areas.map(
        (item) => `${item.position.x},${item.position.y}`,
      );
      const useAutoLayout = valueArr.some(
        (item, index) => valueArr.indexOf(item) !== index,
      );
      if (useAutoLayout) {
        logger.debug(
          `Using auto generated layout for node positions in view ${!parentNodeId ? "facility" : parentNodeId}`,
        );
        const newLayout = getAutoLayoutedElements(
          reactFlowNodes,
          reactFlowEdges,
        );
        reactFlowNodes = newLayout.nodes;
        reactFlowEdges = newLayout.edges;

        // send PUT requests for each node since we've changed their positions
        reactFlowNodes.forEach((node) => {
          ictApi.queryClient.fetchQuery(
            ictApi.client.queryOptions(
              "put",
              "/inventory/process-flow/areas/{areaId}",
              {
                params: {
                  path: { areaId: node.id },
                  query: { view: parentNodeId || "facility" },
                },
                body: {
                  id: node.id,
                  position: { x: node.position.x, y: node.position.y },
                },
              },
            ),
          );
        });
      }
      setNodes(reactFlowNodes);
      setEdges(reactFlowEdges);
    }
  }, [processFlowData, parentNodeId]);

  // Some custom handling of certain types of nodes to get the nodetype
  const getNodeTypeForView = useCallback(
    (label: string, view: string): string => {
      if (view === "multishuttle" && label.toLowerCase().includes("aisle")) {
        return "ict-node";
      }
      switch (label) {
        case "Area":
          return "ict-node";
        case "Aisle":
          return "ict-aisle-node";
        default:
          return "ict-node";
      }
    },
    [],
  );

  /**
   * Converts the edges from the API to the Edge type expected by reactflow
   */
  const mapEdgesToReactFlowEdges = useCallback((edges: GraphEdge[]): Edge[] => {
    const formatter = new Intl.NumberFormat();
    return edges.map((edge: GraphEdge) => {
      const metric = edge.metrics[0];
      const reactFlowEdge: Edge = {
        id: edge.id,
        source: edge.source,
        target: edge.target,
        data: {
          isBiDirectional: false,
          rateDirection: edge.direction,
          rateStatus: "good",
        },
      };

      if (metric) {
        const rateValue =
          typeof metric?.value === "number"
            ? parseFloat(metric.value.toFixed(2))
            : null;
        reactFlowEdge.data.rateUnits = metric?.units ?? "units/hr";
        reactFlowEdge.data.rateUnits = metric?.units ?? "units/hr";
        reactFlowEdge.data.rateValue =
          rateValue !== null ? formatter.format(rateValue) : null;
      }
      return reactFlowEdge;
    });
  }, []);

  /**
   * Converts the nodes from the API to the Node type expected by reactflow
   */
  const mapNodesToReactFlowNodes = useCallback(
    (view: string, nodes: Area[]): Node[] => {
      const graphName = view === "" ? "facility" : view;
      const nodeConfig: NodeConfig = processFlowGraphConfig?.[graphName] ?? {};

      return nodes.map((node) => {
        let nodeOptions: NodeOptions = { metrics: [], hasChildren: false };

        // This is a temporary workaround to get the node options for a StationLocation node.
        // Once our graph configuration has been completed we will be fetching this config
        // from the API and we can remove this along with the similar workaround that is in
        // the NodeDetailPanel component.
        // Determine if this node is a StationLocation node.
        if (node.label.match("^M.*GTP-[0-9]{2}[A-Z][0-9]$")) {
          // Determine if it's a source location or destination location.
          if (node.label.match("D1$")) {
            // Node is a source location
            nodeOptions = nodeConfig?.["source-location"] ?? {};
          } else {
            // Node is a destination location
            nodeOptions = nodeConfig?.["destination-location"] ?? {};
          }
        } else {
          // Node is not a StationLocation
          nodeOptions = nodeConfig?.[node.label.toLowerCase()] ?? {};
        }

        const visibleMetrics: string[] = nodeOptions?.metrics ?? [];
        const hasChildren: boolean = nodeOptions?.hasChildren ?? false;
        const metrics: Metric[] = [];

        visibleMetrics.forEach((metricId: string) => {
          const metric: Metric | undefined = node.metrics.find((m: Metric) =>
            m.id.includes(metricId),
          );
          if (metric) {
            metrics.push(metric);
          }
        });

        return {
          id: node.id,
          position: node.position,
          data: {
            areaId: node.label,
            alerts: node.alerts ? node.alerts : [],
            aisleLevels: node.aisleLevels ?? [],
            label: resolveNodeName(node.label),
            nodeType: getNodeTypeForView(node.nodeType, view),
            metrics,
            hasChildren,
          },
        };
      });
    },
    [processFlowGraphConfigSetting, getNodeTypeForView],
  );

  /**
   * When a node is moved in the canvas, update the neo4j database with the new coordinates
   */
  const handleNodeDrop = useCallback(
    async (node: Node) => {
      const x: number = node.position.x;
      const y: number = node.position.y;

      const nodeId = node.id;
      try {
        const areaNode = processFlowData?.areas.find(
          (n: Area) => n.id === nodeId,
        );
        if (!areaNode) {
          logger.error(`Node with id ${nodeId} not found.`);
          return;
        }

        areaNode.position = { x, y };
        await ictApi.queryClient.fetchQuery(
          ictApi.client.queryOptions(
            "put",
            "/inventory/process-flow/areas/{areaId}",
            {
              params: {
                path: { areaId: nodeId },
                query: { view: parentNodeId || "facility" },
              },
              body: {
                id: nodeId,
                position: { x, y },
              },
            },
          ),
        );
      } catch (error) {
        // show snackbar on api error
        toastManager.addToast({
          id: "node-update-error",
          title: "Error",
          message: `Error updating the position of the ${node.data.label} node`,
          type: "error",
          duration: 3000,
        });

        logger.error("Failed to update node position:", error, node);
      }
    },
    [processFlowData, parentNodeId],
  );

  const handleInteractiveChange = () => {
    // This is a custom method because we are not using reactflow's built in interaction control.
    setIsInteractive(!isInteractive);
  };

  const onFitView = () => {
    sessionStorage.removeItem("PROCESS_FLOW_ZOOM_LEVEL");
  };

  const onMoveEnd: OnMove = (_event, viewport) => {
    sessionStorage.setItem("PROCESS_FLOW_ZOOM_LEVEL", JSON.stringify(viewport));
  };

  const handleEdgeSelect = (edgeId: string) => {
    // Transfer the action of an invisible clickable edge being clicked, to its corresponding visible edge being selected.
    // Then puts the corresponding visible edge into selected state, and calls the onEdgeSelect() callback.

    const correspondingEdgeId = edgeId.split("-clickable-edge")[0];
    const correspondingEdge = edges.find(
      (edge: Edge<FloatingEdgePropsData>) => edge.id === correspondingEdgeId,
    );

    if (correspondingEdge) {
      // Put the corresponding visible edge into a selected state.
      setSelectedEdgeId(correspondingEdgeId);
      // Build edge label from node labels for source node and target node.
      const sourceNode = nodes.find(
        (n: Node) => n.id === correspondingEdge.source,
      );
      const targetNode = nodes.find(
        (n: Node) => n.id === correspondingEdge.target,
      );
      const correspondingEdgeLabel = `${sourceNode?.data.label}-${targetNode?.data.label}`;
      onEdgeSelect(
        correspondingEdgeId,
        correspondingEdgeLabel,
        correspondingEdge.data.isBiDirectional ?? false,
      );
    } else {
      logger.info("Corresponding edge not found for clickable edge.");
    }
  };

  const handleNodeSelect = (nodeId: string, nodeLabel: string) => {
    // Puts clicked node into selected state, and then calls the onNodeSelect() callback.
    setSelectedEdgeId("");
    onNodeSelect(nodeId, nodeLabel);
  };

  const closeDetailPanel = () => {
    onClearSelection();
  };

  const onPaneClick = () => {
    // Clicking anywhere other than on a node or edge removes selected state from any edge or node and then calls the onClearSelection() callback.
    setSelectedEdgeId("");
    onClearSelection();
  };

  const processEdgeData = (
    rawEdges: Edge<FloatingEdgePropsData>[],
  ): Edge<FloatingEdgePropsData>[] => {
    // This method takes the raw edge data from API and processes it into the format needed to render lines and markers.
    const processedEdges: Edge[] = [];

    // Bidirectional pairs of edges will be displayed together as part of the first edge rendered.
    // The corresponding "return" edge will be excluded from the processed edge data to be rendered into lines.
    // The corresponding "return" edge's label data will get added in to the first edge's data and the label
    // will be rendered along with the first edge's label.  (See IctFloatingEdge.tsx)
    const edgeIdsToExclude: string[] = [];

    rawEdges.forEach((edge: Edge<FloatingEdgePropsData>) => {
      if (!edgeIdsToExclude.includes(edge.id)) {
        const processedEdge: Edge<FloatingEdgePropsData> = {
          ...edge,
          type: "floating-edge",
          animated:
            edgeAnimationEffectEnabled && edge.data?.rateStatus === "good",
          data: {
            ...edge?.data,
            edgeLabelsEnabled,
            edgeLabelAlertsEnabled,
            edgeLabelStatusIndicatorsEnabled,
          },
          style: edgeStyle,
          selected: edge.id === selectedEdgeId,
        };

        // If this edge is part of a bidirectional edge pair then we need to
        // display two edge markers on this edge, and exclude rendering the other edge.
        const biDirectionalEdge = rawEdges.find(
          (e: Edge) => e.source === edge.target && e.target === edge.source,
        );
        if (biDirectionalEdge) {
          if (processedEdge.data) {
            processedEdge.data.biDirectionalEdgeData = {
              rateAlerts: biDirectionalEdge.data?.rateAlerts ?? "",
              rateDirection: biDirectionalEdge.data?.rateDirection ?? "",
              rateStatus: biDirectionalEdge.data?.rateStatus ?? "",
              rateUnits: biDirectionalEdge.data?.rateUnits ?? "",
              rateValue: biDirectionalEdge.data?.rateValue ?? "",
            };
          }
          edgeIdsToExclude.push(biDirectionalEdge.id);
        }
        processedEdges.push(processedEdge);

        // Each visible edge needs an invisible clickable edge overlaid on top of it.
        // The reason is, the visible edges are often displayed as dashed and are in motion,
        // and a click event does not register if you click the space between two dashes even
        // if it is over the path of the line.  This makes selecting an edge quite a challenge.
        const clickableTransparentEdge = {
          id: `${edge.id}-clickable-edge`,
          source: edge.source,
          target: edge.target,
          type: "clickable-transparent-edge",
          data: {
            handleEdgeSelect,
          },
        };
        processedEdges.push(clickableTransparentEdge);
      }
    });
    return processedEdges;
  };

  const processNodeData = (rawNodes: Node[]): Node[] => {
    /* This method adds stateful data into the node custom data dict field. */

    return rawNodes.map((n: Node) => {
      return {
        ...n,
        data: {
          ...n.data,
          handleNodeSelect,
          closeDetailPanel,
          isInteractive,
          areaId: n.data.areaId,
          nodeAlertsEnabled,
          nodeDrilldownButtonEnabled,
          nodeMetricsEnabled,
        },
        type: n.data.nodeType || "ict-node",
      };
    });
  };

  /**
   * Render error state
   */
  const renderError = () => {
    return (
      <div className={styles.errorContainer}>
        <WidgetNotification title={"Error loading data"} kind="error" />
      </div>
    );
  };

  /**
   * Render empty state
   */
  const renderNoData = () => {
    return (
      <div className={styles.emptyState}>
        <WidgetNotification title={"No data available"} kind="info" />
      </div>
    );
  };

  let parsedZoomLevel = undefined;
  const zoomLevel = sessionStorage.getItem("PROCESS_FLOW_ZOOM_LEVEL");
  if (zoomLevel) parsedZoomLevel = JSON.parse(zoomLevel);

  if (isLoading) {
    return <LoadingView />;
  } else if (isNoDataAvailable) {
    return renderNoData();
  } else if (error && !isRefetchError) {
    // Only show error if we haven't had a successful response yet
    return renderError();
  }

  return (
    <>
      <div style={{ height: "calc(100vh - 260px)" }}>
        <ReactFlow
          data-testid="react-flow"
          defaultViewport={parsedZoomLevel}
          edges={edges.length > 0 ? processEdgeData(edges) : []}
          edgeTypes={edgeTypes}
          fitView={true}
          nodes={nodes.length > 0 ? processNodeData(nodes) : []}
          nodeTypes={nodeTypes}
          nodesDraggable={isInteractive}
          nodesFocusable={false}
          nodeDragThreshold={1}
          onEdgesChange={onEdgesChange}
          onMoveEnd={onMoveEnd}
          onNodeDragStop={(_event, node) => handleNodeDrop(node)}
          onNodesChange={onNodesChange}
          onPaneClick={onPaneClick}
          panOnDrag={true}
          proOptions={{ hideAttribution: true }}
          ref={reactFlowRef}
          zoomOnDoubleClick={false}
          zoomOnScroll={true}
          selectionMode={SelectionMode.Partial}
          selectionKeyCode={isInteractive ? "Shift" : null}
        >
          <CustomControls
            handleInteractiveChange={handleInteractiveChange}
            isAdmin={hasConfiguratorAccess}
            isInteractive={isInteractive}
            onFitView={onFitView}
          />
        </ReactFlow>
      </div>
    </>
  );
}

export default FacilityProcessFlowChart;
