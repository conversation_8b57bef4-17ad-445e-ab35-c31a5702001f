import {
  Accordion,
  AccordionI<PERSON>,
  <PERSON>ton,
  Column,
  Dropdown,
  FormGroup,
  Grid,
  Heading,
  InlineNotification,
  Loading,
  NumberInput,
  TextArea,
  TextInput,
  Toggle,
} from "@carbon/react";
import { useEffect, useState } from "react";
import {
  AppConfigSetting,
  AppConfigSettingDataType,
  AppConfigSettingSource,
} from "@ict/sdk/types";
import { Checkmark } from "@carbon/icons-react";
import { useRoles } from "../../auth/hooks/use-roles";
import { ictApi } from "../../api/ict-api";
import { OptionsContainer } from "../../components/options/options-container/options-container";
import {
  deleteConfigSetting,
  updateConfigSetting,
} from "../../config/hooks/use-config";
import { toastManager } from "../../components/toast/toast-container";
import { logger } from "../../utils/logger";
import { SettingLogHistory } from "./components/setting-log-history";
import classes from "./app-config-setting-detail.module.css";
interface AppConfigSettingDetailProps {
  settingId?: string;
  onClose: () => void;
}

type SettingValue = {
  source: AppConfigSettingSource;
  value: unknown;
  displayValue?: unknown; // only used for JSON datatypes to display the prettified version
  changed?: boolean;
  valueError: string;
  new: boolean;
};

interface ExtendedAppConfigSetting extends AppConfigSetting {
  values: SettingValue[];
  dirty: boolean;
}

type DropdownItem = {
  label: string;
  value: string;
};

// if settingId is not given, we open a dialog to create a new setting
export const AppConfigSettingDetail = ({
  settingId,
  onClose,
}: AppConfigSettingDetailProps) => {
  const { hasConfiguratorAccess } = useRoles();
  const [setting, setSetting] = useState<ExtendedAppConfigSetting>();
  const [selectedDataType, setSelectedDataType] = useState<DropdownItem>({
    label: "String",
    value: "string",
  });

  const { data, isLoading, error } = ictApi.client.useQuery(
    "get",
    `/config/settings`,
    {
      params: {
        query: {
          settingId,
          allStoredValues: true,
        },
      },
    },
    { enabled: !!settingId },
  );

  // When data is loaded, we need to convert the values to the correct type.  The API responds
  // with multiple setting objects, so we convert it to a single setting object with a values array
  useEffect(() => {
    // create a new setting
    if (!settingId) {
      setSetting({
        id: "",
        name: "",
        group: null,
        description: null,
        dataType: "string",
        source: AppConfigSettingSource.default,
        values: [
          {
            source: AppConfigSettingSource.default,
            changed: false,
            valueError: "",
            new: true,
            value: undefined,
          },
        ],
        dirty: false,
      });
      return;
    }
    if (!data) return;
    const allSettingValues = Array.isArray(data)
      ? data
      : [data as AppConfigSetting];
    if (allSettingValues.length > 0) {
      const mySetting = allSettingValues[0] as ExtendedAppConfigSetting;
      mySetting.values = allSettingValues.map((v) => ({
        source: v.source as AppConfigSettingSource,
        value: v.value,
        displayValue:
          mySetting.dataType === "json"
            ? JSON.stringify(v.value, undefined, 4)
            : v.value,
        changed: false,
        valueError: "",
        new: false,
      }));
      mySetting.dirty = false;

      setSetting(mySetting);
    }
  }, [data]);

  // Helper function to get the most relevant setting value
  // returns the user value if it exists and is not new
  // otherwise returns the tenant value if it exists and is not new
  // otherwise returns the default value
  const getMostRelevantSettingValue = (setting: ExtendedAppConfigSetting) => {
    const userSettingValue = setting.values.find((v) => v.source === "user");
    if (userSettingValue && !userSettingValue.new) {
      return userSettingValue;
    }
    const facilitySettingValue = setting.values.find(
      (v) => v.source === "facility",
    );
    if (facilitySettingValue && !facilitySettingValue.new) {
      return facilitySettingValue;
    }
    const tenantSettingValue = setting.values.find(
      (v) => v.source === "tenant",
    );
    if (tenantSettingValue && !tenantSettingValue.new) {
      return tenantSettingValue;
    }
    return setting.values.find((v) => v.source === "default");
  };

  // Helper function to get the setting value for a given source
  const getSettingValue = (
    source: AppConfigSettingSource,
  ): SettingValue | undefined => {
    return setting?.values.find((v) => v.source === source);
  };

  const handleJsonBeautify = (source: AppConfigSettingSource) => {
    if (!setting) return;
    const value = getSettingValue(source);
    if (!value) return;
    try {
      const displayValue = JSON.stringify(value.value, undefined, 4);

      const updatedValues = setting.values.map((v) => {
        if (v.source === source) {
          const newSettingValue = {
            ...v,
            displayValue,
          };

          return newSettingValue;
        } else {
          return v;
        }
      });

      setSetting({
        ...setting,
        values: updatedValues,
      });
    } catch (e) {
      console.error("Error while attempting to beautify JSON", e);
      return;
    }
  };

  /**
   * Handles changing a setting value
   * @param source - The source of the setting value to change (default, user, tenant, facility)
   * @param newValue - The new value
   */
  const handleValueChange = (
    source: AppConfigSettingSource,
    newValue: string | number | boolean | object,
  ) => {
    if (!setting) return;

    const updatedValues = setting.values.map((v) => {
      if (v.source === source) {
        const newSettingValue = {
          ...v,
          value: newValue,
          changed: true,
          displayValue: newValue,
        };

        if (setting.dataType === "json") {
          try {
            newSettingValue.value = JSON.parse(newValue as string);
            newSettingValue.valueError = "";
          } catch (e) {
            logger.error("Error parsing JSON", e);
            newSettingValue.valueError = "Invalid JSON";
          }
          return newSettingValue;
        }

        return newSettingValue;
      } else {
        return v;
      }
    });
    setSetting({
      ...setting,
      values: updatedValues,
    });
  };

  /**
   * Handles adding a new setting value
   */
  const handleAddValue = (
    _e: React.MouseEvent<HTMLButtonElement>,
    typeToAdd: AppConfigSettingSource,
  ) => {
    if (!setting) return;

    const newValue: SettingValue = {
      source: typeToAdd,
      value: "",
      valueError: "",
      new: true,
      changed: true,
    };

    setSetting({
      ...setting,
      values: [...setting.values, newValue],
    });
  };

  /**
   * Handles saving the setting.  We need to make separate calls for the default, tenant, and user values
   * if they are dirty.
   */
  const handleSave = async () => {
    // ensure the setting has a name
    if (!setting?.name) {
      toastManager.addToast({
        id: "setting-name-required",
        title: "Error",
        message: "Setting name is required!",
        type: "error",
        duration: 3000,
      });
      return;
    }

    // ensure we cannot save empty json values
    // other types are guaranteed to not be empty due to the input field (numbers, booleans)
    // strings are allowed to be empty
    if (setting?.dataType === "json") {
      let hasEmptyValue = false;
      const updatedValues = setting.values.map((v) => {
        if (v.value == undefined || v.value == null || v.value === "") {
          hasEmptyValue = true;
          return {
            ...v,
            valueError: "You must enter a valid JSON value!",
          };
        } else {
          return v;
        }
      });

      // do not save if empty json values are present
      if (hasEmptyValue) {
        setSetting({ ...setting, values: updatedValues });
        return;
      }
    }

    // attempt to save setting
    try {
      const values = setting?.values.filter((v) => v.changed || v.new);
      if (!values) return;
      for (const v of values) {
        await updateConfigSetting({
          ...(setting as AppConfigSetting),
          source: v.source,
          value: v.value,
        });
      }

      toastManager.addToast({
        id: "setting-saved",
        title: "Success",
        message: "Setting saved successfully!",
        type: "success",
        duration: 3000,
      });
    } catch (e: unknown) {
      logger.error("Error saving setting", e);
      // show snack bar on error, with special message for schema validation error
      const errorMessage =
        e instanceof Error &&
        e.stack?.includes("Error validating new json value against schema!")
          ? "New value does not match the setting JSON schema."
          : "";
      toastManager.addToast({
        id: "setting-saved-error",
        title: "Error",
        message: `Failed to save setting. ${errorMessage}`,
        type: "error",
        duration: 3000,
      });
    }
  };

  /**
   * Handles deleting a setting value
   * @param source - The source of the setting value to delete (default, user, tenant)
   */
  const handleDelete = async (source: AppConfigSettingSource) => {
    if (!setting) return;
    const settingValueToDelete = getSettingValue(source);
    if (settingValueToDelete?.new) {
      // if it's new, just remove it.  no need to call the API
      const updatedValues = setting?.values.filter((v) => v.source !== source);
      setSetting({ ...setting, values: updatedValues ?? [] });
    } else {
      // delete the setting value
      try {
        const response = await deleteConfigSetting({ ...setting, source });
        if (response.recordsDeleted > 0) {
          // show a snackbar
          toastManager.addToast({
            id: "setting-value-deleted",
            title: "Setting value deleted",
            message: "The setting value has been deleted",
            type: "success",
            duration: 3000,
          });

          // if it was the default value, it deletes everything so close the panel
          if (source === "default") onClose();
        }
      } catch (e) {
        logger.error("Error deleting setting value", e);
        // show a snackbar on error
        toastManager.addToast({
          id: "setting-value-deleted-error",
          title: "Error",
          message: `Error deleting setting value.`,
          type: "error",
          duration: 3000,
        });
      }
    }
  };

  /**
   * Handles changes to the setting name, group, or description
   * @param field - The field to change
   * @param value - The new value
   */
  function handleSettingChange(field: string, value: string): void {
    if (!setting) return;
    if (field === "name") {
      setSetting({
        ...setting,
        name: value,
        dirty: true,
      });
    } else if (field === "group") {
      setSetting({
        ...setting,
        group: value,
        dirty: true,
      });
    } else if (field === "description") {
      setSetting({
        ...setting,
        description: value,
        dirty: true,
      });
    } else if (field === "dataType") {
      setSelectedDataType({
        label:
          value === "json"
            ? "JSON"
            : value.charAt(0).toUpperCase() + value.slice(1),
        value,
      });
      setSetting({
        ...setting,
        values: [
          {
            source: AppConfigSettingSource.default,
            changed: false,
            valueError: "",
            new: true,
            value: value === "number" ? 0 : undefined,
          },
        ],
        dataType: value as AppConfigSettingDataType,
        dirty: true,
      });
    }
  }

  const renderValueInput = (source: AppConfigSettingSource) => {
    if (!setting) return null;
    const value = getSettingValue(source);
    if (!value) return null;
    switch (setting.dataType) {
      case "boolean":
        return (
          <Toggle
            id={`${source}-boolean-toggle`}
            data-testid={`boolean-input`}
            toggled={value.value === true || String(value.value) === "true"}
            onToggle={(toggled) => handleValueChange(source, toggled)}
          />
        );
      case "number":
        return (
          <NumberInput
            id={`${source}-number-input`}
            data-testid={`number-input`}
            value={Number(value.value)}
            onChange={(_e, { value }) => handleValueChange(source, value)}
          />
        );
      case "json":
        return (
          <>
            <div className={classes.actionButton}>
              <Button
                data-testid={"beautify-button"}
                kind="tertiary"
                type="button"
                onClick={() => handleJsonBeautify(source)}
              >
                Beautify
              </Button>
            </div>
            <TextArea
              id={`${source}-json-textarea`}
              data-testid={`json-input`}
              labelText=""
              value={value.displayValue as string}
              onChange={(e) => handleValueChange(source, e.target.value)}
              rows={10}
              warn={!!value.valueError}
              warnText={value.valueError}
            />
          </>
        );
      default:
        return (
          <TextInput
            id={`${source}-text-input`}
            data-testid={`string-input`}
            labelText=""
            value={value.value as string}
            onChange={(e) => handleValueChange(source, e.target.value)}
          />
        );
    }
  };

  if (error) {
    return (
      <InlineNotification
        kind="error"
        title="Error"
        subtitle={`Error loading setting details: ${
          (error as Error)?.message || String(error)
        }`}
      />
    );
  }

  if (isLoading || !setting) {
    return <Loading />;
  }

  return (
    <div className={classes.optionContainer}>
      <OptionsContainer
        onClose={onClose}
        onSave={handleSave}
        saveButtonText="Save"
        saveButtonDisabled={
          (!setting?.values.some((v) => v.changed) &&
            setting?.dirty === false) ||
          setting?.values.some((v) => v.valueError)
        }
      >
        <Grid className={classes.bottomPadding}>
          <Column lg={16} md={8} sm={4}>
            <div className={classes.drawerHeader}>
              <Heading>
                {settingId ? "Edit App Setting" : "Add New App Setting"}
              </Heading>
            </div>

            <FormGroup legendId="config-setting-details-group" legendText="">
              {settingId && (
                <TextInput
                  id="setting-id"
                  labelText="ID"
                  value={setting.id}
                  className={classes.bottomMargin}
                  disabled={true}
                  readOnly={true}
                />
              )}
              <TextInput
                id="name-text-input"
                labelText="Name"
                value={setting.name}
                className={classes.bottomMargin}
                onChange={(e) => handleSettingChange("name", e.target.value)}
              />
              <TextInput
                id="group-text-input"
                labelText="Group"
                value={setting.group ?? undefined}
                className={classes.bottomMargin}
                onChange={(e) => handleSettingChange("group", e.target.value)}
              />
              <TextInput
                id="description-text-input"
                labelText="Description"
                value={setting.description ?? undefined}
                className={classes.bottomMargin}
                onChange={(e) =>
                  handleSettingChange("description", e.target.value)
                }
              />
              {settingId ? (
                <TextInput
                  id="data-type-text-input"
                  labelText="Data Type"
                  value={setting.dataType ?? undefined}
                  className={classes.bottomMargin}
                  disabled={true}
                  readOnly={true}
                />
              ) : (
                <Dropdown
                  id="setting-type-dropdown"
                  label=""
                  titleText="Data Type"
                  selectedItem={selectedDataType}
                  items={[
                    {
                      label: "String",
                      value: "string",
                    },
                    {
                      label: "JSON",
                      value: "json",
                    },
                    {
                      label: "Number",
                      value: "number",
                    },
                    {
                      label: "Boolean",
                      value: "boolean",
                    },
                  ]}
                  onChange={(e) => {
                    handleSettingChange(
                      "dataType",
                      e.selectedItem?.value || "",
                    );
                  }}
                />
              )}
            </FormGroup>
            <div className={classes.topMargin}>
              <Heading className={classes.bottomMargin}>Values</Heading>
              <Accordion>
                {Object.keys(AppConfigSettingSource).map((key) => {
                  if (
                    getSettingValue(key as AppConfigSettingSource) === undefined
                  )
                    return null;
                  return (
                    <AccordionItem
                      key={key}
                      title={
                        <div className={classes.accordionItemHeader}>
                          <span>
                            {key.charAt(0).toUpperCase() +
                              key.slice(1) +
                              " value"}
                            {getSettingValue(key as AppConfigSettingSource)
                              ?.changed && "*"}
                          </span>
                          {getMostRelevantSettingValue(setting)?.source ===
                            key && <Checkmark color="green" size="24" />}
                        </div>
                      }
                    >
                      <div>
                        <span>
                          {renderValueInput(key as AppConfigSettingSource)}
                        </span>
                        <div className={classes.actionButton}>
                          <Button
                            kind="danger--tertiary"
                            type="button"
                            onClick={() =>
                              handleDelete(key as AppConfigSettingSource)
                            }
                          >
                            Delete {key} value
                          </Button>
                        </div>
                      </div>
                    </AccordionItem>
                  );
                })}
              </Accordion>

              {hasConfiguratorAccess && (
                <div className={classes.valueConfigContainer}>
                  {!setting.values.find((v) => v.source === "tenant") && (
                    <Button
                      data-testid="add-tenant-value-button"
                      onClick={(event) =>
                        handleAddValue(event, AppConfigSettingSource.tenant)
                      }
                    >
                      Add Tenant Value
                    </Button>
                  )}
                  {!setting.values.find((v) => v.source === "facility") && (
                    <Button
                      data-testid="add-facility-value-button"
                      onClick={(event) =>
                        handleAddValue(event, AppConfigSettingSource.facility)
                      }
                    >
                      Add Facility Value
                    </Button>
                  )}
                  {!setting.values.find((v) => v.source === "user") && (
                    <Button
                      data-testid="add-user-value-button"
                      onClick={(event) =>
                        handleAddValue(event, AppConfigSettingSource.user)
                      }
                    >
                      Add User Value
                    </Button>
                  )}
                </div>
              )}
            </div>
            {settingId && (
              <SettingLogHistory
                settingId={settingId}
                settingType={setting.dataType}
              />
            )}
          </Column>
        </Grid>
      </OptionsContainer>
    </div>
  );
};
