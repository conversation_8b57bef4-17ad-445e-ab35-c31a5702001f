import { createColumnHelper } from "@tanstack/react-table";
import { useMemo } from "react";
import { ictApi } from "../../../../api/ict-api";
import { Datagrid } from "../../../../components/datagrid";
import { useTranslation } from "react-i18next";
import { useApiErrorState } from "../../../../hooks/use-api-error-state";

export type CuratedDataTableProps = {
  tableId: string;
};

type CuratedDataColumn = {
  name: string;
  renderType: "string" | "number" | "boolean" | "null";
  value: unknown;
};

type CuratedDataRow = {
  columns: CuratedDataColumn[];
};

export function CuratedDataTable({ tableId }: CuratedDataTableProps) {
  const { t } = useTranslation();
  const { data, error, isLoading, isFetching, refetch } =
    ictApi.client.useQuery(
      "get",
      "/config/v2/curated-data",
      {
        params: {
          query: { table: tableId },
        },
      },
      { enabled: !!tableId },
    );

  const isNoDataAvailable = useApiErrorState(error);

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Transform the data to flatten it for the table
  const tableData = useMemo(() => {
    if (!data) return [];

    return data.map((row: CuratedDataRow) => {
      const flatRow: Record<string, unknown> = {};

      // Parse the data JSON string if it exists
      if (row.columns) {
        for (const col of row.columns) {
          if (col.name === "data" && col.value) {
            try {
              const parsedData = JSON.parse(col.value as string)[0];

              for (const [key, value] of Object.entries(parsedData)) {
                flatRow[`data_${key}`] = value;
              }
            } catch (_e) {
              flatRow[col.name] = col.value;
            }
          } else {
            flatRow[col.name] = col.value;
          }
        }
      }

      return flatRow;
    });
  }, [data]);

  // Define column helper for type safety
  const columnHelper = createColumnHelper<Record<string, unknown>>();

  // Generate columns dynamically based on the first row of data
  const columns = useMemo(() => {
    if (!tableData.length) return [];

    return Object.keys(tableData[0]).map((key) =>
      columnHelper.accessor(key, {
        header: key.startsWith("data_") ? key.replace("data_", "") : key,
        size: 200,
      }),
    );
  }, [tableData, columnHelper]);

  // if (isLoading || isFetching) return <LoadingView />;

  let errorMessage = undefined;
  if (!isNoDataAvailable && error) {
    errorMessage = t("curatedDataTable.errorLoadingData", "No data available");
  }

  return (
    <Datagrid
      columns={columns}
      error={errorMessage}
      isLoading={isLoading || isFetching}
      data={tableData}
      mode="client"
      enableSelection={false}
      initialPagination={{ pageIndex: 0, pageSize: 25 }}
      initialDensity="default"
      showRefreshButton={true}
      onRefreshClick={handleRefresh}
    />
  );
}
