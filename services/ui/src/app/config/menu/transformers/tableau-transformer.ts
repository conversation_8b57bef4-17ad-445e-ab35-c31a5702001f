import { SecurityRoles } from "@ict/sdk/types";
import {
  TableauApiClient,
  TableauView,
  TableauWorkbook,
} from "../../../api/tableau";
import { Logger } from "../../../utils";
import { useFacilityStore } from "../../../stores/facility-store";
import type {
  MenuTransformer,
  MenuTransformerContext,
} from "../menu-transformers";
import type { MenuItem } from "@ict/sdk/types";
import type { AppConfigSetting } from "../types";
import { TableauApiUtil } from "../../../api/tableau/tableau-api-util";

const logger = new Logger("tableau-transformer");

export const TABLEAU_MENU_MAIN_PATH_SEGMENT = "/tableau";

/**
 * Transforms menu items by injecting Tableau dashboard items for users with appropriate permissions.
 * This transformer will only modify the menu if the user has the 'ct_tableau_explorer' role.
 * Workbooks are filtered based on the selected facility's corresponding Tableau project.
 */
export const tableauTransformer: MenuTransformer = async (
  menuItems: MenuItem[],
  context: MenuTransformerContext,
): Promise<MenuItem[]> => {
  try {
    logger.debug("Transforming menu items for Tableau", context);

    const solutionAnalyticsItem = findMenuItemById(
      menuItems,
      "performance-analysis",
    );
    if (!solutionAnalyticsItem) {
      return menuItems;
    }

    const roles = [
      SecurityRoles.CT_TABLEAU_ADMIN,
      SecurityRoles.CT_TABLEAU_EXPLORER,
      SecurityRoles.CT_TABLEAU_VIEWER,
    ];

    // If the user does not have any of the tableau roles, do not inject the tableau dashboards
    if (!context.roles.some((role) => roles.includes(role as SecurityRoles))) {
      return menuItems;
    }

    solutionAnalyticsItem.children = await getTableauMenuItems(
      context.configSettings || [],
    );
    return menuItems;
  } catch (error) {
    logger.error("Error in Tableau transformer:", error);
    return menuItems;
  }
};

/**
 * Retrieves and formats Tableau workbooks and views as menu items.
 * Filters workbooks based on the selected facility's corresponding Tableau project.
 *
 * @param settings - Application configuration settings
 * @returns Array of menu items representing Tableau workbooks and their views
 */
const getTableauMenuItems = async (
  settings: AppConfigSetting[],
): Promise<MenuItem[]> => {
  const client = new TableauApiClient(import.meta.env.VITE_TABLEAU_SERVER_URL);
  await client.signIn();

  const [workbooks, views, projects] = await Promise.all([
    client.getWorkbooksForSite(),
    client.getViewsForSite(),
    client.getProjectsForSite(),
  ]);

  let workbookData = workbooks.workbooks.workbook;
  const viewData = views.views.view;
  const projectData = projects.projects.project;

  // Get the selected facility from the store
  const { selectedFacility } = useFacilityStore.getState();

  if (selectedFacility) {
    // Use the facility's tableauProject field if available, otherwise fall back to facility name
    const projectName =
      selectedFacility.tableauProject || selectedFacility.name;

    workbookData = TableauApiUtil.filterWorkbooksByTopLevelProject(
      workbookData,
      projectData,
      projectName,
    );
  } else {
    // Fallback to the existing top-level project filter if no facility is selected
    const topLevelProjectName = settings.find(
      (setting) => setting.id === "tableau-top-level-project-name",
    )?.value as string | undefined;

    if (topLevelProjectName) {
      workbookData = TableauApiUtil.filterWorkbooksByTopLevelProject(
        workbookData,
        projectData,
        topLevelProjectName,
      );
    }
  }

  return createMenuItemsFromWorkbooks(workbookData, viewData, client.siteName);
};

/**
 * Creates menu items from Tableau workbooks and their views.
 */
const createMenuItemsFromWorkbooks = (
  workbooks: TableauWorkbook[],
  views: TableauView[],
  siteName?: string,
): MenuItem[] => {
  const workbookMap = new Map(workbooks.map((wb) => [wb.id, wb]));
  const workbookMenuMap = new Map<string, MenuItem>();

  // Create menu items for each view and map them to workbooks
  for (const view of views) {
    const workbook = workbookMap.get(view.workbook.id);
    if (!workbook) continue;

    const linkUrl = `${TABLEAU_MENU_MAIN_PATH_SEGMENT}?${siteName ? `siteName=${siteName}&` : ""}contentUrl=${
      workbook.contentUrl
    }&viewUrlName=${view.viewUrlName}`;

    // Add the name so it can be used in the view tile...
    const linkUrlWithName = `${linkUrl}&viewName=${encodeURIComponent(view.name)}`;
    const viewMenuItem: MenuItem = {
      id: view.id,
      label: view.name,
      link: linkUrlWithName,
    };

    const existingWorkbookMenu = workbookMenuMap.get(workbook.id);
    if (existingWorkbookMenu) {
      existingWorkbookMenu.children?.push(viewMenuItem);
    } else {
      workbookMenuMap.set(workbook.id, {
        id: workbook.id,
        label: workbook.name,
        children: [viewMenuItem],
      });
    }
  }

  return Array.from(workbookMenuMap.values()).sort((a, b) =>
    a.label.localeCompare(b.label),
  );
};

/**
 * Finds a menu item by its ID in the menu tree.
 *
 * @param items - Array of menu items to search
 * @param id - ID of the menu item to find
 * @returns The found menu item or undefined
 */
const findMenuItemById = (
  items: MenuItem[],
  id: string,
): MenuItem | undefined => {
  for (const item of items) {
    if (item.id === id) return item;
    if (item.children?.length) {
      const found = findMenuItemById(item.children, id);
      if (found) return found;
    }
  }
  return undefined;
};
