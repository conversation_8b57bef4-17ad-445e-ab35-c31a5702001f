import type {
  ColumnDef,
  OnChangeFn,
  PaginationState,
  RowSelectionState,
  SortingState,
  VisibilityState,
  Row,
} from "@tanstack/react-table";
import type { MouseEventHandler } from "react";

export type DatagridMode = "client" | "server";
export type LayoutDensity = "compact" | "default" | "relaxed";

export type FilterValue = string | number | boolean | null | undefined;

export interface FilterState {
  globalFilter: string;
  filters: Record<string, FilterValue>;
}

export interface ExportOption {
  id: string;
  text: string;
  disabled?: boolean;
}

/**
 * Export function type for client-side exports with data and columns
 */
export type ClientExportFunction = (
  data?: Record<string, unknown>[],
  columns?: { id: string; label: string }[],
) => void;

/**
 * Simple export function type for basic exports
 */
export type SimpleExportFunction = () => void;

/**
 * Combined export function type supporting both signatures
 */
export type ExportFunction = SimpleExportFunction | ClientExportFunction;

export interface DatagridProps<T extends object> {
  /**
   * TanStack Table column definitions
   */

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  columns: ColumnDef<T, any>[];

  /**
   * Data array for the table (required for client mode)
   */
  data?: T[];

  /**
   * Operation mode for the Datagrid
   * - client: All data is provided and managed internally
   * - server: Data is fetched externally based on pagination/sorting/filtering
   */
  mode?: DatagridMode;

  /**
   * Enable row selection with checkboxes
   */
  enableSelection?: boolean;

  /**
   * Current row selection state (required when enableSelection is true)
   */
  rowSelection?: RowSelectionState;

  /**
   * Callback when row selection changes (required when enableSelection is true)
   */
  onRowSelectionChange?: OnChangeFn<RowSelectionState>;

  /**
   * Callback when export button is clicked
   * Can be either a simple function or one that receives data and columns
   */
  onExport?: ExportFunction;

  /**
   * Export menu options
   */
  exportOptions?: ExportOption[];

  /**
   * Callback when an export action is selected
   */
  onExportAction?: (action: string) => void;

  /**
   * Callback for page changes (server mode)
   */
  onPageChange?: (pagination: PaginationState) => void;

  /**
   * Callback for sorting changes (server mode)
   */
  onSort?: (sorting: SortingState) => void;

  /**
   * Callback for filter changes (server mode)
   */
  onFilter?: (filters: FilterState) => void;

  /**
   * Initial pagination state
   */
  initialPagination?: PaginationState;

  /**
   * Initial sorting state
   */
  initialSorting?: SortingState;

  /**
   * Initial column visibility state
   */
  initialColumnVisibility?: VisibilityState;

  /**
   * Initial layout density
   */
  initialDensity?: LayoutDensity;

  /**
   * Total row count (for server mode pagination)
   */
  totalRows?: number;

  /**
   * Loading state indicator
   */
  isLoading?: boolean;

  /**
   * Error state message
   */
  error?: string;

  /**
   * Custom class name for the container
   */
  className?: string;

  /**
   * Show export button
   */
  showExportButton?: boolean;

  /**
   * Show pagination
   */
  showPagination?: boolean;

  /**
   * Show header
   */
  showHeader?: boolean;

  /**
   * Show refresh button
   */
  showRefreshButton?: boolean;

  /**
   * Callback when refresh button is clicked
   */
  /**
   * Callback when add manual entry button is clicked
   */
  onAddManualEntry?: () => void;

  onRefreshClick?: () => void;

  onClearSelection?: () => void;

  /**
   * Enable expandable rows
   */
  enableExpansion?: boolean;

  /**
   * Expanded row state
   */
  expandedRows?: Record<string, boolean>;

  /**
   * Callback when expanded rows change
   */
  onExpandedRowsChange?: (expandedRows: Record<string, boolean>) => void;

  /**
   * Render expanded row content
   */
  renderExpandedRow?: (row: Row<T>) => React.ReactNode;

  /**
   * Callback when update calculation button is clicked
   */
  onUpdateCalculation?: () => void;

  /**
   * Batch actions to display when rows are selected
   */
  batchActions?: {
    label: string;
    onClick: () => void;
    icon?: React.ElementType;
  }[];
}

export interface DatagridHeaderProps {
  /**
   * Global filter value
   */
  globalFilter: string;

  /**
   * Callback when global filter changes
   */
  onGlobalFilterChange: (value: string) => void;

  /**
   * Current column visibility state
   */
  columnVisibility: VisibilityState;

  /**
   * Callback when column visibility changes
   */
  onColumnVisibilityChange: (visibility: VisibilityState) => void;

  /**
   * Available columns for visibility toggle
   */
  columns: { id: string; label: string }[];

  /**
   * Current layout density
   */
  density: LayoutDensity;

  /**
   * Callback when layout density changes
   */
  onDensityChange: (density: LayoutDensity) => void;

  /**
   * Callback when export button is clicked (as a button click handler)
   */
  onExport?: MouseEventHandler<HTMLButtonElement>;

  /**
   * Export menu options
   */
  exportOptions?: ExportOption[];

  /**
   * Callback when an export action is selected
   */
  onExportAction?: (action: string) => void;

  /**
   * Callback when refresh button is clicked
   */
  onRefreshClick?: () => void;

  /**
   * Custom class name for the header
   */
  className?: string;

  selectedRowCount?: number;
  onClearSelection?: () => void;
  /**
   * Callback when update calculation button is clicked
   */
  onUpdateCalculation?: () => void;
  /**
   * Callback when add manual entry button is clicked
   */
  onAddManualEntry?: () => void;

  /**
   * Batch actions to display when rows are selected
   */
  batchActions?: {
    label: string;
    onClick: () => void;
    icon?: React.ElementType;
  }[];
}

export interface DatagridPaginationProps {
  /**
   * Current page index (0-based)
   */
  pageIndex: number;

  /**
   * Current page size
   */
  pageSize: number;

  /**
   * Total number of rows
   */
  totalRows: number;

  /**
   * Callback when pagination changes
   */
  onPaginationChange: (pagination: PaginationState) => void;

  /**
   * Available page size options
   */
  pageSizeOptions?: number[];

  /**
   * Custom class name for the pagination
   */
  className?: string;
}
