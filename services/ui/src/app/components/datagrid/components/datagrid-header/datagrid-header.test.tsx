import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../../../test-utils";
import { DatagridHeader } from "./datagrid-header";

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

describe("DatagridHeader", () => {
  const mockColumns = [
    { id: "col1", label: "Column 1" },
    { id: "col2", label: "Column 2" },
    { id: "col3", label: "Column 3" },
  ];

  const defaultProps = {
    globalFilter: "",
    onGlobalFilterChange: vi.fn(),
    columnVisibility: { col1: true, col2: true, col3: true },
    onColumnVisibilityChange: vi.fn(),
    columns: mockColumns,
    density: "default" as const,
    onDensityChange: vi.fn(),
    onExport: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders correctly with all props", () => {
    render(<DatagridHeader {...defaultProps} />);

    // Check if search input exists
    expect(screen.getByPlaceholderText("Search Table")).toBeInTheDocument();

    // Check if buttons exist
    expect(screen.getByLabelText("Settings")).toBeInTheDocument();
    expect(screen.getByText("Export")).toBeInTheDocument();
  });

  it("renders with refresh button when onRefreshClick is provided", () => {
    render(<DatagridHeader {...defaultProps} onRefreshClick={vi.fn()} />);

    // Check if refresh button exists
    expect(screen.getByLabelText("Refresh")).toBeInTheDocument();
  });

  it("does not render refresh button when onRefreshClick is not provided", () => {
    render(<DatagridHeader {...defaultProps} />);

    // Check that refresh button doesn't exist
    expect(screen.queryByLabelText("Refresh")).not.toBeInTheDocument();
  });

  it("calls onRefreshClick when refresh button is clicked", () => {
    const onRefreshClick = vi.fn();
    render(
      <DatagridHeader {...defaultProps} onRefreshClick={onRefreshClick} />,
    );

    // Find and click the refresh button
    const refreshButton = screen.getByLabelText("Refresh");
    fireEvent.click(refreshButton);

    // Verify the callback was called
    expect(onRefreshClick).toHaveBeenCalled();
  });

  it("renders without export button when onExport is not provided", () => {
    // We want to intentionally pass in unused "export" prop
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { onExport, ...propsWithoutExport } = defaultProps;
    render(<DatagridHeader {...propsWithoutExport} />);
    expect(screen.queryByText("Export")).not.toBeInTheDocument();
  });

  it("calls onGlobalFilterChange when search input changes", () => {
    render(<DatagridHeader {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText("Search Table");
    fireEvent.change(searchInput, { target: { value: "test filter" } });

    expect(defaultProps.onGlobalFilterChange).toHaveBeenCalledWith(
      "test filter",
    );
  });

  it("opens settings popover when settings button is clicked", () => {
    render(<DatagridHeader {...defaultProps} />);

    const settingsButton = screen.getByLabelText("Settings");
    fireEvent.click(settingsButton);

    // Check if both density and column visibility sections are visible
    expect(screen.getByText("Row Density")).toBeInTheDocument();
    expect(screen.getByText("Column Visibility")).toBeInTheDocument();
  });

  it("calls onColumnVisibilityChange when a column checkbox is toggled", () => {
    render(<DatagridHeader {...defaultProps} />);

    // Open settings popover
    const settingsButton = screen.getByLabelText("Settings");
    fireEvent.click(settingsButton);

    // Find the specific column checkbox by its label
    const columnCheckbox = screen.getByLabelText("Column 1");
    fireEvent.click(columnCheckbox);

    // Check if the callback was called with updated visibility
    expect(defaultProps.onColumnVisibilityChange).toHaveBeenCalledWith({
      col1: false,
      col2: true,
      col3: true,
    });
  });

  it("calls onDensityChange when a density option is selected", () => {
    render(<DatagridHeader {...defaultProps} />);

    // Open settings popover
    const settingsButton = screen.getByLabelText("Settings");
    fireEvent.click(settingsButton);

    // Select compact density
    const compactOption = screen.getByLabelText("Compact");
    fireEvent.click(compactOption);

    expect(defaultProps.onDensityChange).toHaveBeenCalledWith("compact");
  });

  it("calls onExport when export button is clicked", () => {
    render(<DatagridHeader {...defaultProps} />);

    const exportButton = screen.getByLabelText("Export");
    fireEvent.click(exportButton);

    expect(defaultProps.onExport).toHaveBeenCalled();
  });

  it("closes popover when clicking outside", async () => {
    render(
      <div>
        <DatagridHeader {...defaultProps} />
        <div data-testid="outside-element">Outside</div>
      </div>,
    );

    // Open settings popover
    const settingsButton = screen.getByLabelText("Settings");
    fireEvent.click(settingsButton);

    // Verify popover is open
    expect(screen.getByText("Row Density")).toBeInTheDocument();

    // Click outside using mousedown and click events
    const outsideElement = screen.getByTestId("outside-element");
    fireEvent.mouseDown(outsideElement);
    fireEvent.mouseUp(outsideElement);
    fireEvent.click(outsideElement);

    // Wait a bit for the popover to close
    await new Promise((resolve) => setTimeout(resolve, 0));

    // Verify popover is closed by checking if the button is no longer expanded
    const settingsButtonAfterClose = screen.getByLabelText("Settings");
    expect(settingsButtonAfterClose.getAttribute("aria-expanded")).toBe(
      "false",
    );
  });

  it("closes popover when Escape key is pressed", async () => {
    render(<DatagridHeader {...defaultProps} />);

    // Open settings popover
    const settingsButton = screen.getByLabelText("Settings");
    fireEvent.click(settingsButton);

    // Verify popover is open
    expect(screen.getByText("Row Density")).toBeInTheDocument();

    // Press Escape
    fireEvent.keyDown(document, { key: "Escape" });
    fireEvent.keyUp(document, { key: "Escape" });

    // Wait a bit for the popover to close
    await new Promise((resolve) => setTimeout(resolve, 0));

    // Verify popover is closed by checking if the button is no longer expanded
    const settingsButtonAfterClose = screen.getByLabelText("Settings");
    expect(settingsButtonAfterClose.getAttribute("aria-expanded")).toBe(
      "false",
    );
  });
});
