@use "@carbon/colors";

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--cds-border-subtle-01);
  background-color: var(--cds-layer-01);
  position: relative;
}

.leftSection {
  display: flex;
  flex: 1;
  align-items: center;
  gap: 0.5rem;
}

.rightSection {
  display: flex;
  align-items: center;
}

.rightSectionButton {
  min-height: 40px !important;
  width: 40px !important;
  padding: 0 !important;
  border: none !important;
  background-color: transparent !important;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: var(--cds-background-hover) !important;
  }

  &:active {
    background-color: var(--cds-background-active) !important;
  }

  &.buttonOpen {
    background-color: var(--cds-layer-selected-01) !important;
  }

  svg {
    width: 16px;
    height: 16px;
    fill: var(--cds-icon-primary);
  }

  &:focus {
    outline: 2px solid var(--cds-focus);
    outline-offset: -2px;
  }
}

.columnVisibilityPopoverContent {
  padding: 1rem 1rem 0.75rem 1rem;
  box-shadow: var(--cds-shadow-sm);
  max-height: 480px;
  min-width: 240px;
  overflow-y: auto;
  background-color: var(--cds-layer-02);
  border: 1px solid var(--cds-border-subtle-02);
}

.radioButtonGroup {
  padding-bottom: 0.75rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--cds-border-subtle-01);
}

.headerContainer {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: var(--cds-layer);
  border-bottom: 1px solid var(--cds-border-subtle);
  position: sticky;
  top: 0;
  z-index: 2;
  width: 100%;
}

.searchContainer {
  flex: 1;
  margin-right: 1rem;
}

.actionsContainer {
  display: flex;
  gap: 0.5rem;
}

// Ensure all dropdowns have proper z-index

.selectionTag {
  margin-left: 0.5rem;
  display: flex;
  align-items: center;
}

.exportContainer {
  position: relative;
}

.exportMenu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.25rem;
  background-color: var(--cds-layer-02);
  border: 1px solid var(--cds-border-subtle-02);
  border-radius: 4px;
  box-shadow: var(--cds-shadow-sm);
  z-index: 9999;
  min-width: 10rem;

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  button {
    width: 100%;
    text-align: left;
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    color: var(--cds-text-primary);
    cursor: pointer;

    &:hover {
      background-color: var(--cds-layer-hover-02);
    }

    &:active {
      background-color: var(--cds-layer-active-02);
    }

    &:disabled {
      color: var(--cds-text-disabled);
      cursor: not-allowed;
    }
  }
}

.checkbox {
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.popoverOpen {
  :global(.cds--popover) {
    background-color: var(--cds-layer-02);
  }
}

.customSelectionTag {
  background-color: var(--cds-background-brand);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 0 0 1rem;
  text-align: center;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customSelectionTagButton {
  color: var(--cds-text-primary);
}

.buttonsContainer {
  display: flex;
  align-items: center;
}

.batchActionsHeader {
  z-index: 10;
  // "x item(s) selected" text
  div > p > span {
    color: colors.$gray-100;
  }
  // Buttons, actions/cancel
  div > button {
    // Divider between actions and cancel
    &::before {
      background-color: colors.$gray-100 !important;
    }
    // Button text/icon
    color: colors.$gray-100 !important;
  }
}
