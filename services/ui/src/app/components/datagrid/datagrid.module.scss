.datagridContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  border: 1px solid var(--cds-border-subtle-00);
  background-color: var(--cds-background);

  &.disabled {
    pointer-events: none;
    opacity: 0.5;
  }
}

.tableContainer {
  flex: 1 1 auto;
  overflow-y: auto;
  overflow-x: auto;
  position: relative;
  min-height: 0;

  table {
    width: 100%;
  }
}

.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed;

  th,
  td {
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.875rem;
    padding: 0 1rem;
    color: var(--cds-text-primary);
  }

  th {
    background-color: var(--cds-layer-accent);
    border-bottom: 1px solid var(--cds-border-subtle-01);
    font-weight: 600;

    &:hover {
      background-color: var(--cds-layer-accent-hover);
    }

    &:active {
      background-color: var(--cds-layer-accent-active);
    }

    &[data-sorted="true"] {
      background-color: var(--cds-background-active);
    }
  }

  td {
    border-bottom: 1px solid var(--cds-border-subtle);
  }

  thead {
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  tr {
    background-color: var(--cds-layer-01);

    &:hover {
      background-color: var(--cds-layer-hover-01);
    }
  }
}

.sortable {
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: var(--cds-layer-hover-02);
  }

  &:hover .sortIcon {
    opacity: 1;
    color: var(--cds-icon-secondary);
  }

  &[data-sorted="true"] .sortIcon {
    opacity: 1;
    color: var(--cds-icon-primary);
  }
}

.sortIcon {
  vertical-align: middle;
  opacity: 0;
  color: var(--cds-icon-secondary);
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  cursor: default;

  > span:first-child {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
  }
}

.sortable .headerContent {
  cursor: pointer;
}

.resizer {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 6px;
  background: var(--cds-background-hover);
  cursor: col-resize;
  user-select: none;
  touch-action: none;
  opacity: 0;
  transition: opacity 0.2s;

  &:hover,
  &.isResizing {
    opacity: 1;
    background: var(--cds-interactive);
  }
}

.isResizing {
  background: var(--cds-interactive);
  opacity: 1;
}

// Density styles - exact heights with centered text
.compact {
  th,
  td {
    height: 32px;
    line-height: 32px;
  }
}

.default {
  th,
  td {
    height: 40px;
    line-height: 40px;
    padding: 3px 6px;
  }
}

.relaxed {
  th,
  td {
    height: 48px;
    line-height: 48px;
  }
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  pointer-events: none;
}

.emptyState {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
