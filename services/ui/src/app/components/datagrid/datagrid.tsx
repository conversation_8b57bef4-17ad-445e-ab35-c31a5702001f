import {
  <PERSON><PERSON><PERSON>D<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Down,
  ArrowsVertical,
} from "@carbon/icons-react";
import { Loading } from "@carbon/react";
import type {
  Cell,
  ColumnFiltersState,
  HeaderContext,
  HeaderGroup,
  PaginationState,
  Row,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import React, { useEffect, useMemo, useState } from "react";
import { DatagridExportModal } from "./components/datagrid-export-modal/datagrid-export-modal";
import { DatagridHeader } from "./components/datagrid-header/datagrid-header";
import { DatagridPagination } from "./components/datagrid-pagination/datagrid-pagination";
import styles from "./datagrid.module.scss";
import type {
  DatagridProps,
  FilterState,
  FilterValue,
  LayoutDensity,
  ClientExportFunction,
  SimpleExportFunction,
} from "./types";
import { useTranslation } from "react-i18next";
import { WidgetNotification } from "../widget-container/widget-notification";

/**
 * Datagrid component provides a flexible data table with sorting, filtering, pagination,
 * and row selection capabilities. It supports both client-side and server-side data processing.
 *
 * @template T - The type of data objects in the table
 * @param columns - Column definitions for the table
 * @param data - Array of data objects to display
 * @param mode - Data processing mode: "client" or "server"
 * @param enableSelection - Whether to enable row selection
 * @param rowSelection - Current row selection state
 * @param onRowSelectionChange - Callback when row selection changes
 * @param onExport - Callback for export functionality
 * @param onPageChange - Callback when page changes (server mode)
 * @param onSort - Callback when sorting changes (server mode)
 * @param onFilter - Callback when filters change
 * @param initialPagination - Initial pagination state
 * @param initialSorting - Initial sorting state
 * @param initialColumnVisibility - Initial column visibility state
 * @param initialDensity - Initial layout density
 * @param totalRows - Total number of rows (for server mode)
 * @param isLoading - Whether data is currently loading
 * @param error - Error message if data loading failed
 * @param className - Additional CSS class
 * @param showExportButton - Whether to show the export button
 * @param showPagination - Whether to show pagination controls
 * @param showHeader - Whether to show the table header
 * @param showRefreshButton - Whether to show the refresh button
 * @param onRefreshClick - Callback for refresh functionality
 * @param onClearSelection - Callback to clear row selection
 * @param renderExpandedRow - Callback to render expanded row content
 * @param enableExpansion - Whether to enable row expansion
 * @param expandedRows - Current expanded row state
 * @param onExpandedRowsChange - Callback when expanded rows change
 * @param onAddManualEntry - Callback for adding a manual entry
 * @param onUpdateCalculation - Callback for updating calculation
 */
export function Datagrid<T extends object>({
  columns,
  data = [],
  mode = "client",
  enableSelection = false,
  rowSelection = {},
  onRowSelectionChange,
  onExport,
  exportOptions,
  onExportAction,
  onPageChange,
  onSort,
  onFilter,
  initialPagination = { pageIndex: 0, pageSize: 10 },
  initialSorting = [],
  initialColumnVisibility = {},
  initialDensity = "default",
  totalRows = 0,
  isLoading = false,
  error,
  className,
  showExportButton = true,
  showPagination = true,
  showHeader = true,
  showRefreshButton = false,
  onRefreshClick,
  onClearSelection,
  onAddManualEntry,
  onUpdateCalculation,
  enableExpansion = false,
  expandedRows = {},
  onExpandedRowsChange,
  renderExpandedRow,
  batchActions,
}: DatagridProps<T> & {
  enableExpansion?: boolean;
  expandedRows?: Record<string, boolean>;
  onExpandedRowsChange?: (expandedRows: Record<string, boolean>) => void;
  renderExpandedRow?: (row: Row<T>) => React.ReactNode;
}) {
  const { t } = useTranslation();
  // State management
  const [globalFilter, setGlobalFilter] = useState("");
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>(initialSorting);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    initialColumnVisibility,
  );
  const [pagination, setPagination] =
    useState<PaginationState>(initialPagination);
  const [density, setDensity] = useState<LayoutDensity>(initialDensity);

  // Add state for export modal
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);

  /**
   * Create table columns with optional selection and expansion columns
   */
  const tableColumns = useMemo(() => {
    let cols = columns;

    if (enableExpansion) {
      cols = [
        {
          id: "expand",
          header: () => null,
          cell: ({ row }: { row: Row<T> }) => (
            <button
              onClick={() => {
                const newExpandedRows = {
                  ...expandedRows,
                  [row.id]: !expandedRows[row.id],
                };
                onExpandedRowsChange?.(newExpandedRows);
              }}
              className={styles.expandButton}
              aria-label={expandedRows[row.id] ? "Collapse row" : "Expand row"}
              style={{
                background: "none",
                border: "none",
                cursor: "pointer",
                padding: 0,
              }}
            >
              <ChevronDown
                className={expandedRows[row.id] ? styles.expanded : ""}
                style={{
                  transition: "transform 0.2s",
                  transform: expandedRows[row.id]
                    ? "rotate(-180deg)"
                    : undefined,
                }}
              />
            </button>
          ),
          enableSorting: false,
          size: 40,
        },
        ...cols,
      ];
    }

    if (enableSelection) {
      cols = [
        {
          id: "selection",
          header: ({ table }: HeaderContext<T, unknown>) => (
            <input
              type="checkbox"
              checked={table.getIsAllRowsSelected()}
              onChange={table.getToggleAllRowsSelectedHandler()}
              aria-label="Select all rows"
            />
          ),
          cell: ({ row }: { row: Row<T> }) => (
            <input
              type="checkbox"
              checked={row.getIsSelected()}
              onChange={row.getToggleSelectedHandler()}
              aria-label={`Select row ${row.id}`}
            />
          ),
          enableSorting: false,
          size: 40,
        },
        ...cols,
      ];
    }

    return cols;
  }, [
    columns,
    enableSelection,
    enableExpansion,
    expandedRows,
    onExpandedRowsChange,
  ]);

  /**
   * Initialize and configure the TanStack table
   */
  const table = useReactTable({
    data,
    columns: tableColumns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      columnVisibility,
      pagination: showPagination
        ? pagination
        : { pageIndex: 0, pageSize: data.length },
      rowSelection: enableSelection ? rowSelection : {},
    },
    pageCount:
      mode === "server"
        ? Math.ceil(totalRows / pagination.pageSize)
        : undefined,
    manualPagination: mode === "server",
    manualSorting: mode === "server",
    manualFiltering: mode === "server",
    enableRowSelection: enableSelection,
    onRowSelectionChange: onRowSelectionChange,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(), // Always enable sorting for both client and server modes
    getFilteredRowModel: getFilteredRowModel(), // Always enable filtering for both client and server modes
    getPaginationRowModel: showPagination ? getPaginationRowModel() : undefined, // Only enable pagination if showPagination is true
    columnResizeMode: "onChange", // Update column sizes during resize instead of after
    enableColumnResizing: true,
  });

  /**
   * Handle server-side pagination
   */
  useEffect(() => {
    if (mode === "server") {
      onPageChange?.(pagination);
    }
  }, [mode, pagination, onPageChange]);

  /**
   * Handle server-side sorting
   */
  useEffect(() => {
    if (mode === "server") {
      onSort?.(sorting);
    }
  }, [mode, sorting, onSort]);

  /**
   * Handle filtering (both client and server modes)
   */
  useEffect(() => {
    // Create filter state object
    const filterState: FilterState = {
      globalFilter,
      filters: columnFilters.reduce<Record<string, FilterValue>>(
        (acc, filter) => {
          acc[filter.id] = filter.value as FilterValue;
          return acc;
        },
        {},
      ),
    };

    if (mode === "server") {
      // Debounce the filter creation to prevent excessive API calls
      const finalFilters = setTimeout(() => {
        setPagination((prev) => ({
          // Reset pagination to first page when filters change
          ...prev, // This prevents "no results" scenarios when filtering reduces total items
          pageIndex: 0, // lower than the current page index, ensures users always see relevant results
        }));
        onFilter?.(filterState);
      }, 1000);
      return () => {
        clearTimeout(finalFilters);
      };
    } else {
      // Call onFilter immediately for client mode
      onFilter?.(filterState);
    }
  }, [globalFilter, columnFilters, onFilter]);

  /**
   * Get visible columns for column visibility menu
   */
  const visibleColumns = useMemo(() => {
    return table
      .getAllLeafColumns()
      .filter((col) => col.id !== "selection")
      .map((col) => ({
        id: col.id,
        label:
          typeof col.columnDef.header === "string"
            ? col.columnDef.header
            : col.id,
      }));
  }, [table.getAllLeafColumns]);

  // Calculate actual total rows based on mode
  const actualTotalRows =
    mode === "server" ? totalRows : table.getFilteredRowModel().rows.length;

  /**
   * Get CSS class for current density setting
   */
  const densityClass = useMemo(() => {
    switch (density) {
      case "compact":
        return styles.compact;
      case "relaxed":
        return styles.relaxed;
      default:
        return styles.default;
    }
  }, [density]);

  // UI state flags
  const isError = error;
  const isNoData = !isLoading && !isError && actualTotalRows === 0;

  // Calculate number of selected rows
  const selectedRowCount = enableSelection
    ? Object.values(rowSelection).filter(Boolean).length
    : 0;

  // Handle clearing all row selections
  const handleClearSelection = () => {
    if (onClearSelection) {
      onClearSelection();
    } else if (onRowSelectionChange) {
      onRowSelectionChange({});
    }
  };

  /**
   * Render loading state
   */
  const renderLoading = () => {
    return (
      <div className={styles.loadingOverlay}>
        <Loading
          active
          description={t("datagrid.loadingData", "Loading data...")}
          withOverlay={false}
        />
      </div>
    );
  };

  /**
   * Render error state
   */
  const renderError = () => {
    return (
      <div className={styles.errorContainer}>
        <WidgetNotification
          title={t(
            "datagrid.errorLoadingData",
            "Error loading data: {{error}}",
            {
              error: error,
            },
          )}
          kind="error"
        />
      </div>
    );
  };

  /**
   * Render empty state
   */
  const renderNoData = () => {
    return (
      <div className={styles.emptyState}>
        <WidgetNotification
          title={t("datagrid.noDataAvailable", "No data available")}
          kind="info"
        />
      </div>
    );
  };

  /**
   * Render the table with headers and rows
   */
  const renderTable = () => {
    return (
      <table
        data-testid="data-grid-table"
        className={`${styles.table} ${densityClass}`}
      >
        <thead>
          {table.getHeaderGroups().map((headerGroup: HeaderGroup<T>) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  colSpan={header.colSpan}
                  style={{
                    width: header.getSize() + 40,
                    position: "relative",
                  }}
                  className={header.column.getCanSort() ? styles.sortable : ""}
                  data-sorted={header.column.getIsSorted() ? "true" : "false"}
                  data-column-id={header.column.id}
                  title={
                    header.column.getIsSorted()
                      ? `${header.column.columnDef.header as string} (${
                          header.column.getIsSorted() === "asc"
                            ? t("datagrid.ascending", "Ascending")
                            : t("datagrid.descending", "Descending")
                        })`
                      : (header.column.columnDef.header as string)
                  }
                >
                  <div
                    className={styles.headerContent}
                    onClick={
                      header.column.getCanSort()
                        ? header.column.getToggleSortingHandler()
                        : undefined
                    }
                    onKeyDown={(e) => {
                      if (
                        header.column.getCanSort() &&
                        (e.key === "Enter" || e.key === " ")
                      ) {
                        e.preventDefault();
                        const handler = header.column.getToggleSortingHandler();
                        if (handler) {
                          handler(e);
                        }
                      }
                    }}
                    tabIndex={header.column.getCanSort() ? 0 : undefined}
                    role={header.column.getCanSort() ? "button" : undefined}
                  >
                    {header.isPlaceholder ? null : (
                      <>
                        <span>
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                        </span>
                        {header.column.getCanSort() && (
                          <span className={styles.sortIcon}>
                            {header.column.getIsSorted() ? (
                              {
                                asc: <ArrowUp />,
                                desc: <ArrowDown />,
                              }[header.column.getIsSorted() as string]
                            ) : (
                              <ArrowsVertical />
                            )}
                          </span>
                        )}
                      </>
                    )}
                  </div>
                  {header.column.getCanResize() && (
                    <div
                      className={`${styles.resizer} ${
                        header.column.getIsResizing() ? styles.isResizing : ""
                      }`}
                      onMouseDown={header.getResizeHandler()}
                      onTouchStart={header.getResizeHandler()}
                      onClick={(e) => e.stopPropagation()}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ") {
                          e.preventDefault();
                        }
                      }}
                      tabIndex={0}
                      role="separator"
                      aria-orientation="vertical"
                      aria-valuenow={header.getSize()}
                    />
                  )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row: Row<T>) => (
            <>
              <tr key={row.id}>
                {row.getVisibleCells().map((cell: Cell<T, unknown>) => (
                  <td key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
              {enableExpansion && expandedRows[row.id] && renderExpandedRow && (
                <tr key={`${row.id}-expanded`} className={styles.expandedRow}>
                  <td colSpan={row.getVisibleCells().length}>
                    {renderExpandedRow(row)}
                  </td>
                </tr>
              )}
            </>
          ))}
        </tbody>
      </table>
    );
  };

  /**
   * Handle export functionality
   */
  const handleExport = () => {
    // If custom export handler is provided, use it
    if (onExport) {
      if (mode === "client") {
        // For client mode, call with data and columns (ClientExportFunction signature)
        (onExport as ClientExportFunction)(exportData, exportColumns);
      } else {
        // For server mode, call without parameters (SimpleExportFunction signature)
        // Parent handles server-side filtering and data processing
        (onExport as SimpleExportFunction)();
      }
      return;
    }

    // Only open the built-in export modal for client-side tables
    if (mode === "client") {
      setIsExportModalOpen(true);
    }
  };

  /**
   * Create a mouse event handler wrapper for the export functionality
   */
  const handleExportClick = showExportButton
    ? (event: React.MouseEvent<HTMLButtonElement>) => {
        event.preventDefault();
        handleExport();
      }
    : undefined;

  // Get column information for the export modal
  const exportColumns = useMemo(() => {
    return columns
      .filter((col) => col.id !== "selection") // Exclude selection column
      .map((col) => ({
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        id: (col.id || (col as any).accessorKey) as string,
        // Use header if available, otherwise use id or accessorKey
        label:
          typeof col.header === "string"
            ? col.header
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
              ((col.id || (col as any).accessorKey) as string),
      }));
  }, [columns]);

  // Fix type error with data
  const exportData = useMemo(() => {
    // For client mode: use filtered rows from the table
    // For server mode: use the original data (server has already done the filtering)
    const sourceData =
      mode === "client"
        ? table.getFilteredRowModel().rows.map((row) => row.original)
        : data;

    // Convert data to Record<string, unknown>[] type
    return sourceData.map((item) => {
      // Convert each object to a Record with string keys
      const record: Record<string, unknown> = {};
      for (const key in item) {
        // Fix the 'any' type error by using proper typing
        record[key] = (item as Record<string, unknown>)[key];
        // if the field is an array, stringify it so it shows up
        if (Array.isArray(record[key]) && record[key].length > 0) {
          record[key] = record[key]
            .map((item: Record<string, unknown>) =>
              Object.entries(item)
                .map(([k, v]) => `${k}: ${v}`)
                .join(", "),
            )
            .join(" | ");
        }
      }
      return record;
    });
  }, [data, mode, globalFilter, columnFilters]);

  return (
    <div
      data-testid="datagrid-container"
      className={`${styles.datagridContainer} ${className || ""} ${
        isLoading ? styles.disabled : ""
      }`}
    >
      {showHeader && (
        <DatagridHeader
          data-testid="data-grid-header"
          globalFilter={globalFilter}
          onGlobalFilterChange={setGlobalFilter}
          columnVisibility={columnVisibility}
          onColumnVisibilityChange={setColumnVisibility}
          columns={visibleColumns}
          density={density}
          onDensityChange={setDensity}
          onExport={handleExportClick}
          exportOptions={exportOptions}
          onExportAction={onExportAction}
          onRefreshClick={showRefreshButton ? onRefreshClick : undefined}
          selectedRowCount={selectedRowCount}
          onClearSelection={enableSelection ? handleClearSelection : undefined}
          onAddManualEntry={onAddManualEntry}
          onUpdateCalculation={onUpdateCalculation}
          batchActions={batchActions}
        />
      )}

      <div className={styles.tableContainer}>
        {isError && renderError()}
        {isNoData && renderNoData()}
        {!isError && !isNoData && renderTable()}
      </div>

      {isLoading && renderLoading()}

      {showPagination && (
        <DatagridPagination
          data-testid="data-grid-pagination"
          pageIndex={table.getState().pagination.pageIndex}
          pageSize={table.getState().pagination.pageSize}
          totalRows={actualTotalRows}
          onPaginationChange={table.setPagination}
        />
      )}

      {/* Export Modal */}
      {mode === "client" && (
        <DatagridExportModal
          isOpen={isExportModalOpen}
          onClose={() => setIsExportModalOpen(false)}
          data={exportData}
          columns={exportColumns}
          fileName="table_export"
          selectedRows={enableSelection ? rowSelection : undefined}
          totalRows={actualTotalRows}
          columnVisibility={columnVisibility}
        />
      )}
    </div>
  );
}
