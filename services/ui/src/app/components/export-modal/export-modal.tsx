import React, { useState, useEffect, useRef } from "react";
import ReactDOM from "react-dom";
import {
  Modal,
  TextInput,
  Checkbox,
  Button,
  Tag,
  Form,
  Stack,
  Loading,
} from "@carbon/react";
import { ColumnFiltersState } from "@tanstack/react-table";
import styles from "./export-modal.module.scss";

interface ExportModalProps {
  open: boolean;
  onClose: () => void;
  onExport: (
    fileName: string,
    columns: Record<string, boolean>,
    signal: AbortSignal,
  ) => Promise<void>;
  filters: ColumnFiltersState;
  columnDefs: { id: string; header: string }[];
  baseFileName?: string;
  globalFilter?: string;
}
export function ExportModal({
  open,
  onClose,
  onExport,
  filters,
  columnDefs,
  baseFileName = "Table_List_Export",
  globalFilter = "",
}: ExportModalProps) {
  const [fileName, setFileName] = useState(baseFileName);
  const [isExporting, setIsExporting] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  const [portalTarget, setPortalTarget] = useState<HTMLElement | null>(null);

  useEffect(() => {
    const target = document.getElementById("global-modal-portal");
    if (target) {
      setPortalTarget(target);
    } else {
      // Fallback to body if the specific portal div isn't found for some reason
      setPortalTarget(document.body);
    }
  }, []);

  // Initialize and reset selected columns (same as your existing logic)
  const [selectedColumns, setSelectedColumns] = useState<
    Record<string, boolean>
  >({});

  useEffect(() => {
    if (open) {
      setSelectedColumns(
        columnDefs.reduce(
          (acc, col) => {
            acc[col.id] = true;
            return acc;
          },
          {} as Record<string, boolean>,
        ),
      );
    }
  }, [open, columnDefs]);

  const [helperText] = useState(
    "Current date and .xlsx extension will be appended to the filename",
  );

  // Reset filename when baseFileName changes
  useEffect(() => {
    setFileName(baseFileName);
  }, [baseFileName]);

  // Select or deselect all columns
  const handleSelectAll = () => {
    const newSelection = { ...selectedColumns };
    columnDefs.forEach((col) => {
      newSelection[col.id] = true;
    });
    setSelectedColumns(newSelection);
  };
  const handleDeselectAll = () => {
    const newSelection = { ...selectedColumns };
    columnDefs.forEach((col) => {
      newSelection[col.id] = false;
    });
    setSelectedColumns(newSelection);
  };
  const handleColumnToggle = (columnId: string, checked: boolean) => {
    setSelectedColumns({
      ...selectedColumns,
      [columnId]: checked,
    });
  };
  const handleSubmit = async () => {
    try {
      setIsExporting(true);

      // Create AbortController for this export
      abortControllerRef.current = new AbortController();

      // Format the filename with date before passing to onExport
      const today = new Date();
      const formattedDate = `${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}`;
      const finalFileName = `${fileName}_${formattedDate}.xlsx`;

      // Pass the AbortSignal to the onExport function
      await onExport(
        finalFileName,
        selectedColumns,
        abortControllerRef.current.signal,
      );

      // Only close the modal if the export was successful
      onClose();
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        console.log("Export cancelled by user");
      } else {
        console.error("Error during export:", error);
      }
    } finally {
      setIsExporting(false);
      abortControllerRef.current = null;
    }
  };
  // Check if form is valid
  const isValid =
    fileName.trim().length > 0 &&
    Object.values(selectedColumns).some((value) => value === true);

  const handleClose = () => {
    // If there's an ongoing export, cancel it
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setIsExporting(false);
    onClose();
  };

  // Extract filter display names
  const filterChips = [
    ...filters.map((filter) => (
      <Tag key={filter.id} type="blue" data-testid="export-filter-tag">
        {filter.id}: {String(filter.value)}
      </Tag>
    )),
    ...(globalFilter && globalFilter.trim() !== ""
      ? [
          <Tag key="global-search" type="blue" data-testid="export-filter-tag">
            {globalFilter}
          </Tag>,
        ]
      : []),
  ];

  const modalContent = (
    <Modal
      open={open}
      modalHeading="Export as Excel"
      primaryButtonText={isExporting ? "Exporting..." : "Export"}
      secondaryButtonText="Cancel"
      primaryButtonDisabled={!isValid || isExporting}
      onRequestClose={handleClose}
      onRequestSubmit={handleSubmit}
      size="lg"
    >
      <Form className={styles.exportModal__form}>
        {isExporting && (
          <div>
            <Loading active={true} withOverlay={true} />
          </div>
        )}
        <Stack gap={5}>
          <div className={styles.exportModal__section}>
            <h4>File Details</h4>
            <TextInput
              id="file-name"
              labelText="File Name"
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              helperText={helperText}
              required
              className={styles.exportModal__fileNameInput}
              disabled={isExporting}
            />
          </div>
          <hr className={styles.exportModal__divider} />
          <div className={styles.exportModal__section}>
            <h4>Filters</h4>
            <div className={styles.exportModal__filtersContainer}>
              {filterChips.length > 0 ? filterChips : <p>No filters applied</p>}
            </div>
          </div>
          <hr className={styles.exportModal__divider} />
          <div className={styles.exportModal__section}>
            <h4>Select Columns</h4>
            <div className={styles.exportModal__columnsControls}>
              <Button
                kind="tertiary"
                size="sm"
                onClick={handleSelectAll}
                disabled={isExporting}
              >
                Select All
              </Button>
              <Button
                kind="tertiary"
                size="sm"
                onClick={handleDeselectAll}
                disabled={isExporting}
              >
                Deselect All
              </Button>
            </div>
            <div className={styles.exportModal__columnsGrid}>
              {columnDefs.map((col) => (
                <Checkbox
                  key={col.id}
                  id={`col-${col.id}`}
                  labelText={col.header}
                  checked={selectedColumns[col.id] || false}
                  onChange={(e) => handleColumnToggle(col.id, e.target.checked)}
                  disabled={isExporting}
                />
              ))}
            </div>
          </div>
        </Stack>
      </Form>
    </Modal>
  );

  if (!open || !portalTarget) {
    return null;
  }

  return ReactDOM.createPortal(modalContent, portalTarget);
}
