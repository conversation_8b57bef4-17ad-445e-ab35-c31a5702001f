import {
  BarChartOptions,
  ScaleTypes,
  SimpleBarChart,
} from "@carbon/charts-react";
import "@carbon/charts/styles.css";
import { useTheme } from "@carbon/react";
import { CategoryChartData } from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import { ThemeMode } from "../../layout/theme";
import { formatCamelCaseToTitleCase } from "../../utils/string-util";
import { UnitUtil, type UnitType } from "../../utils/unit-util";
import styles from "./bar-chart-component.module.css";
import { useMeasure } from "@uidotdev/usehooks";

export type BarOrientation = "horizontal" | "vertical";
export type SortOrder = "none" | "ascending" | "descending";

export interface BarChartDataPoint {
  group: string;
  value: number;
}

export interface BarChartComponentProps {
  chartData: CategoryChartData;
  title?: string;
  orientation?: BarOrientation;
  sortBy?: SortOrder;
  color?: string;
  showAverageLine?: boolean;
  showTargetLine?: boolean;
  targetValue?: number;
  displayLimit?: number;
  showLegend?: boolean;
}

export const BarChartComponent = ({
  chartData,
  title,
  orientation = "vertical",
  sortBy = "none",
  color,
  showAverageLine = false,
  showTargetLine = false,
  targetValue = 0,
  displayLimit,
  showLegend = false,
}: BarChartComponentProps) => {
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;
  const [ref, { height }] = useMeasure();

  if (
    !chartData ||
    !chartData.categoryData ||
    chartData.categoryData.length === 0
  ) {
    return <div>No Chart Data Available</div>;
  }

  // Get the unit from the data
  const unit = chartData.unit as UnitType;

  // Convert chart data to carbon bar chart format
  let barData: BarChartDataPoint[] = chartData.categoryData.map((category) => ({
    group: formatCamelCaseToTitleCase(category.name),
    value: category.value,
  }));

  // Sort the data if needed
  if (sortBy !== "none") {
    barData.sort((a, b) => {
      if (sortBy === "ascending") {
        return a.value - b.value;
      } else {
        return b.value - a.value;
      }
    });
  }

  // Apply display limit if set
  if (displayLimit && displayLimit > 0 && barData.length > displayLimit) {
    barData = barData.slice(0, displayLimit);
  }

  // Get all unique group names
  const allGroups = barData.map((item) => item.group);

  // Build color scale object if color is provided
  let colorScale = {};

  if (color) {
    // Create a color scale object with the provided color for each group
    colorScale = allGroups.reduce((acc, groupName) => {
      return {
        ...acc,
        [groupName]: color,
      };
    }, {});
  }

  // Calculate average value if needed
  let averageValue: number | undefined;
  if (showAverageLine && barData.length > 0) {
    const sum = barData.reduce((total, item) => total + item.value, 0);
    averageValue = sum / barData.length;
  }

  // Create thresholds array for average and target lines
  const thresholds = [];

  if (showAverageLine && averageValue !== undefined) {
    thresholds.push({
      value: averageValue,
      label: `Average: ${UnitUtil.formatValue(averageValue, unit)}`,
      fillColor: isDark ? "#78a9ff" : "#0043ce", // Blue color for average line
    });
  }

  if (showTargetLine && targetValue !== undefined) {
    thresholds.push({
      value: targetValue,
      label: `Target: ${UnitUtil.formatValue(targetValue, unit)}`,
      fillColor: isDark ? "#ff8389" : "#da1e28", // Red color for target line
    });
  }

  // Determine axis title
  const axisTitle = unit && UnitUtil.getUnitLabel(unit);

  // Configure the options based on orientation
  const options: BarChartOptions = {
    title: title,
    animations: true,
    legend: {
      enabled: showLegend,
    },
    toolbar: {
      enabled: false,
    },
    color: color
      ? {
          scale: colorScale,
        }
      : undefined,
    axes:
      orientation === "horizontal"
        ? {
            // Horizontal bar configuration
            left: {
              mapsTo: "group",
              scaleType: ScaleTypes.LABELS,
            },
            bottom: {
              mapsTo: "value",
              title: axisTitle,
              scaleType: ScaleTypes.LINEAR,
              includeZero: true,
              // Add thresholds to the value axis for horizontal orientation
              thresholds: thresholds.length > 0 ? thresholds : undefined,
            },
          }
        : {
            // Vertical bar configuration
            left: {
              mapsTo: "value",
              title: axisTitle,
              scaleType: ScaleTypes.LINEAR,
              includeZero: true,
              // Add thresholds to the value axis for vertical orientation
              thresholds: thresholds.length > 0 ? thresholds : undefined,
            },
            bottom: {
              mapsTo: "group",
              scaleType: ScaleTypes.LABELS,
            },
          },
    height: height ? `${height - 5}px` : undefined,
    width: "100%",
    theme: isDark ? "g100" : "g10",
    tooltip: {
      valueFormatter: (value: number | string, _label: string): string => {
        // Convert to number if it's a string
        const numericValue =
          typeof value === "string" ? parseFloat(value) : value;

        if (!Number.isFinite(numericValue)) return String(value);
        return UnitUtil.formatValue(numericValue, unit, undefined, false);
      },
    },
  };

  // Create a key that includes all properties that should trigger a re-render
  const chartKey = JSON.stringify({
    orientation,
    sortBy,
    color,
    showAverageLine,
    showTargetLine,
    targetValue,
    displayLimit,
  });

  return (
    <div className={styles.chartContainer} ref={ref}>
      <SimpleBarChart key={chartKey} data={barData} options={options} />
    </div>
  );
};

export default BarChartComponent;
