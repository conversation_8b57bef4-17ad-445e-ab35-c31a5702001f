import { useAuth0 } from "@auth0/auth0-react";
import { useNavigate } from "react-router";
import type { ParcelConfig } from "single-spa";
import { mountRootParcel } from "single-spa";
import Parcel from "single-spa-react/parcel";
import { findMfeConfig } from "./mfe-config";
import { moduleLoader } from "./module-loader";
import type { MfeModule } from "./types";

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> is responsible for loading the main micro-frontend application
 */
export const MfeLoader = ({ path }: { path: string }) => {
  const navigate = useNavigate();

  // Get values from contexts
  const auth0Context = useAuth0();

  async function getConfig(): Promise<ParcelConfig> {
    const mfeConfig = findMfeConfig(path);
    if (!mfeConfig) {
      throw new Error(`No MFE configuration found for path: ${path}`);
    }

    let module: MfeModule;
    try {
      module = await moduleLoader.loadModule(mfeConfig.url);
    } catch (error) {
      console.error("Error loading module:", error);
      navigate("/error");
      throw error; // Re-throw to prevent returning undefined module
    }

    return {
      bootstrap: module.bootstrap
        ? () => Promise.resolve(module.bootstrap!())
        : () => Promise.resolve(),
      mount: () => Promise.resolve(module.mount()),
      unmount: () => Promise.resolve(module.unmount()),
    };
  }

  const mfeProps = {
    auth0Context,
  };

  return (
    <Parcel
      key={path}
      mountParcel={mountRootParcel}
      // @ts-expect-error - mfe config is passed with a promise
      config={getConfig()}
      wrapWith="div"
      wrapClassName="mfe-container"
      {...mfeProps}
    />
  );
};
