export interface MenuItem {
  /**
   * The unique identifier for the menu item.
   */
  id: string;
  /**
   * The label for the menu item.
   */
  label: string;
  /**
   * The icon for the menu item. This must be a valid icon
   * from components/dynamic-icon
   */
  icon?: string;
  /**
   * The view type for the menu item. This must be a valid view type
   * from app/views/views-config.ts
   */
  viewType?: string;
  /**
   * The view config id for the menu item. This ID will be used as a setting ID
   * and automatically loaded and injected into your view properties.
   */
  viewConfigId?: string;
  /**
   * The link for the menu item.
   */
  link?: string;
  /**
   * The help link for the menu item. This links to the corresponding help documentation in fluid topics.
   */
  helpLink?: string;
  /**
   * The children for the menu item.
   */
  children?: MenuItem[];
  /**
   * The roles for the menu item.
   */
  roles?: string[];
  /**
   * Whether the menu item is local only.
   */
  localOnly?: boolean;
  /**
   * Whether the menu item is visible.
   */
  visible?: boolean;
  /**
   * Any additional properties for the menu item.
   */
  [key: string]: unknown;
}

export type MenuConfig = MenuItem[];
