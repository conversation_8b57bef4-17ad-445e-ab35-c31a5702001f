import {DataSource, DataSourceOptions} from 'typeorm';
import 'dotenv/config';
import {SettingLogSubscriber} from 'ict-api-schema';
import {DataSourceUtils, EntityTypes} from '../../utils/datasource-utils.ts';

const buildDataSource = async () => {
  // GCP_POSTGRES_SECRET_NAME now contains the actual secret content
  const secretString = process.env.GCP_POSTGRES_SECRET_NAME;

  if (!secretString) {
    throw new Error('GCP_POSTGRES_SECRET_NAME environment variable not set');
  }

  let options: DataSourceOptions;
  try {
    options = JSON.parse(secretString) as DataSourceOptions;
  } catch (error) {
    throw new Error(
      'Failed to parse PostgreSQL configuration from GCP_POSTGRES_SECRET_NAME environment variable',
    );
  }

  const newOptionsDb: DataSourceOptions = {
    type: 'postgres',
    database: process.env.CURRENT_TENANT,
    schema: 'config',
    entities: DataSourceUtils.getEntityListFromEntityType(EntityTypes.Config),
    subscribers: [SettingLogSubscriber],
    migrations: DataSourceUtils.getMigrationListFromEntityType(
      EntityTypes.Config,
    ),
  };

  const newOptions: DataSourceOptions = Object.assign(
    {},
    options,
    newOptionsDb,
  );

  return new DataSource(newOptions);
};

export default buildDataSource();
