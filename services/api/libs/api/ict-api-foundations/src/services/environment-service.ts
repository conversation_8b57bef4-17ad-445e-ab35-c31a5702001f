import {Environment} from '@ict/sdk-foundations/types/index.ts';
import {DiService} from '../di/type-di.ts';

@DiService()

/**
 * Used for accessing environment configuration of the API
 */
export class EnvironmentService {
  constructor() {}

  public get app() {
    return {
      env: process.env.ENVIRONMENT,
      authAudience: process.env.AUTH_AUDIENCE,
      authDomain: process.env.AUTH_DOMAIN,
      sha: process.env.sha,
      unitTest: process.env.UNIT_TEST,
      isProd: process.env.ENVIRONMENT === Environment.prod,
    };
  }

  public get ports() {
    return {
      ai: process.env.AI_API_PORT,
      admin: process.env.ADMIN_API_PORT,
      config: process.env.CONFIG_API_PORT,
      dataExplorer: process.env.DATAEXPLORER_API_PORT,
      diagnostics: process.env.DIAGNOSTICS_API_PORT,
      equipment: process.env.EQUIPMENT_API_PORT,
      inventory: process.env.INVENTORY_API_PORT,
      migrations: process.env.MIGRATIONS_API_PORT,
      operators: process.env.OPERATORS_API_PORT,
      orders: process.env.ORDERS_API_PORT,
      simulation: process.env.SIMULATION_API_PORT,
      tableauProxy: process.env.TABLEAU_PROXY_PORT,
      mock: process.env.MOCK_API_PORT,
      workstation: process.env.WORKSTATION_API_PORT,
      availability: process.env.AVAILABILITY_API_PORT,
    };
  }

  public get cors() {
    return {
      origin: process.env.CORS_ORIGIN,
      defaultOrigin: process.env.DEFAULT_CORS_ORIGIN,
    };
  }

  public get bucket() {
    return {
      name: process.env.BUCKETNAME,
    };
  }

  public get postgresDb() {
    return {
      port: process.env.POSTGRES_PORT,
      host: process.env.POSTGRES_HOST,
      username: process.env.POSTGRES_USERNAME,
      password: process.env.POSTGRES_PASSWORD,
      dbOverride: process.env.DB_OVERRIDE,
      gcpSecretName: process.env.GCP_POSTGRES_SECRET_NAME,
      pgGcpSecretName: process.env.GCP_PG_POSTGRES_SECRET_NAME,
    };
  }

  public get edpBigQueryDb() {
    return {
      projectId: process.env.EDP_BIGQUERY_PROJECT_ID,
    };
  }

  public get apiProject() {
    return {
      projectId: process.env.PROJECT_ID,
    };
  }

  public get tableau() {
    return {
      nodeIp: process.env.TABLEAU_NODE_IP,
    };
  }

  public get redis() {
    return {
      host: process.env.REDISHOST ? process.env.REDISHOST : 'localhost',
      port: process.env.REDISPORT ? process.env.REDISPORT : 6379,
      authString: process.env.REDISAUTHSTRING
        ? process.env.REDISAUTHSTRING
        : '',
      localRequestCacheOverride:
        process.env.REDIS_REQUEST_CACHE_OVERRIDE === 'true',
    };
  }

  public get neo4j() {
    return {
      uri: process.env.NEO4J_URI,
      username: process.env.NEO4J_USERNAME,
      password: process.env.NEO4J_PASSWORD,
    };
  }

  public get enterpriseSearch() {
    return {
      secretsName: process.env.ENTERPRISE_SEARCH_SECRETS_NAME,
      url: process.env.ENTERPRISE_SEARCH_URL,
    };
  }

  public get aimlDataDictionaryTables(): string[] {
    if (!process.env.AIML_TABLES) {
      return [];
    }

    return process.env.AIML_TABLES.split(',');
  }

  public get edpPubSubTopic() {
    return {
      topic: process.env.EDP_PUBSUB_TOPIC_ID,
    };
  }

  public get systemAvailability() {
    return {
      projectId: process.env.SYSTEM_AVAILABILITY_PROJECT_ID,
    };
  }
}
