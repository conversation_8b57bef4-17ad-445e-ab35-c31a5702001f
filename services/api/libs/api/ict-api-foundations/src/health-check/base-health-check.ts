import {BigQuery} from '@google-cloud/bigquery';
import https from 'https';
import fetch from 'node-fetch';
import {createClient} from 'redis';
import {Prettify} from '../defs/pretty.ts';
import {Container} from '../di/type-di.ts';
import {HealthCheckStatus} from '../enums/health-check-status.ts';
import {EnvironmentService} from '../services/environment-service.ts';

const REDIS_HOST = process.env.REDISHOST ?? 'localhost';
const REDIS_PORT = process.env.REDISPORT ?? 6379;
const REDIS_AUTH_STRING = process.env.REDISAUTHSTRING ?? '';
const REDIS_URL_PATH = `redis://default:${REDIS_AUTH_STRING}@${REDIS_HOST}:${REDIS_PORT}`;
const redisClient = createClient({url: REDIS_URL_PATH});

export type AvailableServices = 'auth0' | 'bigQuery' | 'redis';

type FilteredServices<T extends AvailableServices> = {
  [K in T]: HealthCheckStatus;
};

export class BaseHealthCheck {
  public static getBasicHealthCheck() {
    return {
      status: HealthCheckStatus.ONLINE,
      sha: process.env.SHA ?? '',
    };
  }

  static async getDeepHealthCheck<T extends AvailableServices>(
    services: T[]
  ): Promise<{
    status: HealthCheckStatus;
    sha: string;
    subSystems: Prettify<FilteredServices<T>>;
  }> {
    const serviceStatuses = {} as FilteredServices<T>;

    // Get the status for each service
    for (const service of services) {
      // eslint-disable-next-line no-await-in-loop
      serviceStatuses[service] = await this.getServiceStatus(service);
    }

    const envService = Container.get(EnvironmentService);

    const healthStatus = BaseHealthCheck.overallStatus(
      Object.values<HealthCheckStatus>(serviceStatuses)
    );

    return {
      status: healthStatus,
      sha: envService.app.sha ?? '',
      subSystems: serviceStatuses,
    };
  }

  /**
   * Gets the status for the service.
   *
   * To add another service to check the health status of:
   * - Add its name to the `AvailableServices` interface
   * - Add the service name as a case in this switch statement and its status check function
   */
  static async getServiceStatus(service: string): Promise<HealthCheckStatus> {
    switch (service) {
      case 'auth0':
        return BaseHealthCheck.getAuthStatus();
      case 'bigQuery':
        return BaseHealthCheck.getBigQueryStatus();
      case 'redis':
        return BaseHealthCheck.getRedisStatus();
      default:
        return HealthCheckStatus.UNKOWN;
    }
  }

  static async getBigQueryStatus(): Promise<HealthCheckStatus> {
    const bigQueryClient = new BigQuery();
    // simplest query just to make sure we can reach the database
    const sql = 'SELECT 1';
    try {
      await bigQueryClient.query(sql);
      return HealthCheckStatus.ONLINE;
    } catch {
      return HealthCheckStatus.OFFLINE;
    }
  }

  static async getRedisStatus() {
    try {
      if (!redisClient.isReady) {
        await redisClient.connect();
      }
      return redisClient.isReady
        ? HealthCheckStatus.ONLINE
        : HealthCheckStatus.OFFLINE;
    } catch (error) {
      return HealthCheckStatus.OFFLINE;
    }
  }

  static async getAuthStatus(): Promise<HealthCheckStatus> {
    const envService = Container.get(EnvironmentService);
    const httpsAgent = new https.Agent({
      rejectUnauthorized: false,
    });

    try {
      const response = await fetch(
        `https://${envService.app.authDomain ?? ''}`,
        {
          method: 'GET',
          headers: {
            Accept: 'application/json',
          },
          agent: httpsAgent,
        }
      );
      if (response.ok) {
        return HealthCheckStatus.ONLINE;
      }
      return HealthCheckStatus.OFFLINE;
    } catch {
      return HealthCheckStatus.OFFLINE;
    }
  }

  static overallStatus(statuses: HealthCheckStatus[]): HealthCheckStatus {
    const hasOffline = statuses.some(
      status => status === HealthCheckStatus.OFFLINE
    );
    const hasOnline = statuses.some(
      status => status === HealthCheckStatus.ONLINE
    );

    if (!hasOffline) {
      // Everything is online!
      return HealthCheckStatus.ONLINE;
    }
    if (hasOnline && hasOffline) {
      // Some online, some offline
      return HealthCheckStatus.UNHEALTHY;
    }
    // Everything is offline
    return HealthCheckStatus.OFFLINE;
  }
}
