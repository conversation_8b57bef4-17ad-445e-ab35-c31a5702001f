import {DataSourceUtils, EntityTypes} from 'ict-api-schema';
import {DataSource, DataSourceOptions} from 'typeorm';
import {Container} from '../di/type-di.ts';
import {Database} from './database.ts';
import {DatabaseTypes} from './db-types.ts';
import {EnvironmentService} from '../services/environment-service.ts';

/**
 * Object that holds the actual Postgres ORM object and relevant information for a connection.
 */
export class PostgresDatabase extends Database {

  constructor(
    private _datasource: DataSource,
    private entityType: EntityTypes,
  ) {
    super();
  }

  get datasource(): DataSource {
    return this._datasource;
  }

  public getType(): string {
    return `${DatabaseTypes.Postgres}|${this.entityType}`;
  }

  /**
   * Generate default connection options for the Postgres database, using secrets if in a non-development environment.
   * @returns TypeORM DataSourceOptions that can connect to the configured Postgres database.
   */
  public static async generateDefaultPostgresOptions(
    database: string,
    entityType: EntityTypes,
  ): Promise<DataSourceOptions> {
    const envService = Container.get(EnvironmentService);

    // Try to get Postgres configuration from environment variable first
    const postgresSecretString = envService.postgresDb.gcpSecretName;

    if (postgresSecretString) {
      // Use environment variable containing the actual secret content
      let options: DataSourceOptions;
      try {
        options = JSON.parse(postgresSecretString) as DataSourceOptions;
      } catch (error) {
        throw new Error(
          'Failed to parse PostgreSQL configuration from GCP_POSTGRES_SECRET_NAME environment variable',
        );
      }

      const postgresOptions = {
        type: 'postgres',
      };
      options = Object.assign({}, postgresOptions, options);
      options = DataSourceUtils.getDataSourceOptionsWithEntities(
        options,
        entityType,
      );
      options = Object.assign(
        {},
        options,
        DataSourceUtils.getDataSourceSubscribers(entityType),
      );
      return DataSourceUtils.getDataSourceOptionsWithDatabase(
        options,
        database,
      );
    }

    // Fall back to environment variables when no secret is available
    const portStr = envService.postgresDb.port;
    const port = portStr ? parseInt(portStr, 10) : 5432;

    let options: DataSourceOptions = {
      type: 'postgres',
      host: envService.postgresDb.host ?? 'localhost',
      port,
      username: envService.postgresDb.username ?? 'postgres',
      password: envService.postgresDb.password ?? 'devpassword',
      logging: true,
    };
    options = DataSourceUtils.getDataSourceOptionsWithEntities(
      options,
      entityType,
    );
    options = Object.assign(
      {},
      options,
      DataSourceUtils.getDataSourceSubscribers(entityType),
    );
    return DataSourceUtils.getDataSourceOptionsWithDatabase(options, database);
  }
}
