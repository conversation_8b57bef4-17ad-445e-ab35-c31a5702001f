import {DataSourceUtils, EntityTypes} from 'ict-api-schema';
import {DataSource, DataSourceOptions} from 'typeorm';
import {Environment} from '@ict/sdk-foundations/types/index.ts';
import {Container} from '../di/type-di.ts';
import {Database} from './database.ts';
import {DatabaseTypes} from './db-types.ts';
import {GCPSecretManager} from '../secrets/gcp-secret-manager.ts';
import {EnvironmentService} from '../services/environment-service.ts';

/**
 * Object that holds the actual Postgres ORM object and relevant information for a connection.
 */
export class PostgresDatabase extends Database {
  private envService: EnvironmentService;

  constructor(
    private _datasource: DataSource,
    private entityType: EntityTypes,
  ) {
    super();
    this.envService = Container.get(EnvironmentService);
  }

  get datasource(): DataSource {
    return this._datasource;
  }

  public getType(): string {
    return `${DatabaseTypes.Postgres}|${this.entityType}`;
  }

  /**
   * Generate default connection options for the Postgres database, using secrets if in a non-development environment.
   * @returns TypeORM DataSourceOptions that can connect to the configured Postgres database.
   */
  public static async generateDefaultPostgresOptions(
    database: string,
    entityType: EntityTypes,
  ): Promise<DataSourceOptions> {
    const envService = Container.get(EnvironmentService);

    const nodeEnv = envService.app.env;
    if (!nodeEnv || nodeEnv !== Environment.local) {
      // use secret manager here for non-dev environments
      const secretManager = Container.get(GCPSecretManager);
      if (!secretManager) {
        throw new Error(
          'A secret manager is required for connecting to a postgres db in this environment.',
        );
      }

      const secretName = envService.postgresDb.gcpSecretName || '';
      const query = Database.generateDefaultSecretQuery(secretName);
      const secretString = await secretManager.get(query);
      let options = JSON.parse(secretString) as DataSourceOptions;
      const postgresOptions = {
        type: 'postgres',
      };
      options = Object.assign({}, postgresOptions, options);
      options = DataSourceUtils.getDataSourceOptionsWithEntities(
        options,
        entityType,
      );
      options = Object.assign(
        {},
        options,
        DataSourceUtils.getDataSourceSubscribers(entityType),
      );
      return DataSourceUtils.getDataSourceOptionsWithDatabase(
        options,
        database,
      );
    }
    const portStr = envService.postgresDb.port;
    const port = portStr ? parseInt(portStr, 10) : 5432;

    let options: DataSourceOptions = {
      type: 'postgres',
      host: envService.postgresDb.host ?? 'localhost',
      port,
      username: envService.postgresDb.username ?? 'postgres',
      password: envService.postgresDb.password ?? 'devpassword',
      logging: true,
    };
    options = DataSourceUtils.getDataSourceOptionsWithEntities(
      options,
      entityType,
    );
    options = Object.assign(
      {},
      options,
      DataSourceUtils.getDataSourceSubscribers(entityType),
    );
    return DataSourceUtils.getDataSourceOptionsWithDatabase(options, database);
  }
}
