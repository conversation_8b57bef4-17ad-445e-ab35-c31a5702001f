import {EntityTypes} from 'ict-api-schema';

export enum DatabaseTypes {
  BigQuery = 'bigQuery',
  Postgres = 'postgres',
  PgPostgres = 'pgPostgres',
  Neo4j = 'neo4j',
}

export enum DatabaseOptionTemplates {
  DatabaseId = '!~DATABASEID~!',
  DatasetId = '!~DATASETID~!',
  Env_AvailabilityProject = '!~ENV_AVAILABILITY_PROJECT~!',
}

export interface DatabaseOptions {
  database?: string;

  //optional function that can be used to prepare these options (e.g. replace templates/placeholders)
  prepareOptions?: (options?: DatabaseOptions) => DatabaseOptions | undefined;
}

export interface PostgresDatabaseOptions extends DatabaseOptions {
  entityType: EntityTypes;
}

export interface PgPostgresDatabaseOptions extends DatabaseOptions {
  secretName?: string;
  secretProjectId?: string;
}

export const configPostgresDatabase = (): DBMiddlewareDatabaseOption => {
  const dbOptions: PostgresDatabaseOptions = {
    entityType: EntityTypes.Config,
  };
  return {
    type: DatabaseTypes.Postgres,
    options: dbOptions,
  };
};

/**
 * Object that will hold the type and options to use for creating databases for users.
 */
export interface DBMiddlewareDatabaseOption {
  type: DatabaseTypes;
  options?: DatabaseOptions;
}

export class DatabaseMiddlewareOptions {
  /**
   * List of database objects to create. These will define the databases available to the application.
   */
  databases: Array<DBMiddlewareDatabaseOption> = [
    {type: DatabaseTypes.BigQuery},
  ];
}
