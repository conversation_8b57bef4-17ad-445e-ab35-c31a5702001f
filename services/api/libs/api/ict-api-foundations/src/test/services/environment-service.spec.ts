import {expect} from 'chai';
import {EnvironmentService} from '../../services/environment-service.ts';

describe('EnvironmentService', () => {
  let envService: EnvironmentService;

  beforeEach(() => {
    envService = new EnvironmentService();
    // Clean up environment variables before each test
    delete process.env.GCP_REDIS_SECRET_NAME;
    delete process.env.REDISHOST;
    delete process.env.REDISPORT;
    delete process.env.REDISAUTHSTRING;
    delete process.env.REDIS_REQUEST_CACHE_OVERRIDE;
  });

  afterEach(() => {
    // Clean up environment variables after each test
    delete process.env.GCP_REDIS_SECRET_NAME;
    delete process.env.REDISHOST;
    delete process.env.REDISPORT;
    delete process.env.REDISAUTHSTRING;
    delete process.env.REDIS_REQUEST_CACHE_OVERRIDE;
  });

  describe('redis', () => {
    it('should use GCP_REDIS_SECRET_NAME when available', () => {
      const mockRedisConfig = {
        host: 'secret-redis-host',
        port: 6380,
        auth_string: 'secret-auth-string',
        type: 'redis'
      };
      
      process.env.GCP_REDIS_SECRET_NAME = JSON.stringify(mockRedisConfig);
      
      const result = envService.redis;
      
      expect(result.host).to.equal('secret-redis-host');
      expect(result.port).to.equal(6380);
      expect(result.authString).to.equal('secret-auth-string');
      expect(result.localRequestCacheOverride).to.equal(false);
    });

    it('should fall back to individual environment variables when GCP_REDIS_SECRET_NAME is not set', () => {
      process.env.REDISHOST = 'individual-host';
      process.env.REDISPORT = '6381';
      process.env.REDISAUTHSTRING = 'individual-auth';
      process.env.REDIS_REQUEST_CACHE_OVERRIDE = 'true';
      
      const result = envService.redis;
      
      expect(result.host).to.equal('individual-host');
      expect(result.port).to.equal(6381);
      expect(result.authString).to.equal('individual-auth');
      expect(result.localRequestCacheOverride).to.equal(true);
    });

    it('should fall back to individual environment variables when GCP_REDIS_SECRET_NAME contains invalid JSON', () => {
      process.env.GCP_REDIS_SECRET_NAME = 'invalid-json';
      process.env.REDISHOST = 'fallback-host';
      process.env.REDISPORT = '6382';
      process.env.REDISAUTHSTRING = 'fallback-auth';
      
      const result = envService.redis;
      
      expect(result.host).to.equal('fallback-host');
      expect(result.port).to.equal(6382);
      expect(result.authString).to.equal('fallback-auth');
      expect(result.localRequestCacheOverride).to.equal(false);
    });

    it('should use default values when no environment variables are set', () => {
      const result = envService.redis;
      
      expect(result.host).to.equal('localhost');
      expect(result.port).to.equal(6379);
      expect(result.authString).to.equal('');
      expect(result.localRequestCacheOverride).to.equal(false);
    });

    it('should handle missing fields in GCP_REDIS_SECRET_NAME gracefully', () => {
      const incompleteConfig = {
        host: 'partial-host'
        // missing port, auth_string
      };
      
      process.env.GCP_REDIS_SECRET_NAME = JSON.stringify(incompleteConfig);
      
      const result = envService.redis;
      
      expect(result.host).to.equal('partial-host');
      expect(result.port).to.equal(6379); // default
      expect(result.authString).to.equal(''); // default
      expect(result.localRequestCacheOverride).to.equal(false);
    });

    it('should preserve localRequestCacheOverride from environment variable even when using secret', () => {
      const mockRedisConfig = {
        host: 'secret-host',
        port: 6380,
        auth_string: 'secret-auth'
      };
      
      process.env.GCP_REDIS_SECRET_NAME = JSON.stringify(mockRedisConfig);
      process.env.REDIS_REQUEST_CACHE_OVERRIDE = 'true';
      
      const result = envService.redis;
      
      expect(result.host).to.equal('secret-host');
      expect(result.port).to.equal(6380);
      expect(result.authString).to.equal('secret-auth');
      expect(result.localRequestCacheOverride).to.equal(true);
    });
  });
});
