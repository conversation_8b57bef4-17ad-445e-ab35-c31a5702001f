# Control Tower API

This monorepo contains all of the Control Tower API packages.

## Table of Contents

- [Control Tower API](#control-tower-api)
  - [Table of Contents](#table-of-contents)
  - [Setting up for Local Development](#setting-up-for-local-development)
    - [Using MISE](#using-mise)
      - [Mise commands](#mise-commands)
    - [Database Setup](#database-setup)
    - [Usage](#usage)
    - [Docker Container (preferred)](#docker-container-preferred)
    - [Improved Testing Experience (Visual Studio Code)](#improved-testing-experience-visual-studio-code)
    - [Debugging Locally](#debugging-locally)
  - [Adding Package Dependencies](#adding-package-dependencies)
  - [Prettier (VSCode)](#prettier-vscode)
  - [Terraform Operations](#terraform-operations)
    - [Pre-requisites](#pre-requisites)
    - [Planning Terraform Environment](#planning-terraform-environment)
    - [Planning Terraform Resources for a specific environment](#planning-terraform-resources-for-a-specific-environment)
    - [Applying Terraform](#applying-terraform)
    - [CI/CD Pipeline](#cicd-pipeline)
    - [ENV](#env)
    - [Request Caching](#request-caching)
    - [Using Bruno to Make API Requests](#using-bruno-to-make-api-requests)
  - [Project Structure](#project-structure)

## Setting up for Local Development

If you are working on any part of the monorepo, you will need to bootstrap to ensure that all of the packages are synced and linked together with yarn, as API packages will have dependencies to other packages within the monorepo, such as the Foundations package.

To do this:

- Have Node 22+ installed: <https://nodejs.org/en/download/>
- Ensure your terminal's current directory is set to the root "/control-tower-api/" folder.
- Run these commands:
  - `corepack enable`
  - `yarn install`
  - `yarn run setup`

If you have a globally installed version of yarn that gets used instead of the repo specific version
run `yarn set version 4.3.1` and re-run the above yarn commands.

You WILL run into compilation errors when compiling the Typescript for API packages if you do not perform this process, as the compiler won't know where to find the locally linked packages that are dependencies to API packages.

### Using MISE

For building and running locally you can also use the [MISE tool](https://mise.jdx.dev/)

When entering the control-tower-api project for the first time, install project dependencies using

```bash
mise install
```

#### Mise commands

- **mise run install** - installs project dependencies and sets up yarn
- **mise run build** - builds all projects
- **mise run run-local** - builds and runs all projects using docker compose
- **mise run test** - runs unit tests
- **mise run test:coverage** - runs tests with code coverage
- **mise run lint** - runs linting
- **mise run test:smoke** - runs smoke tests
- **mise run publish-sdk** - publishes the SDK
- **mise run clean** - cleans project code

### Database Setup

To set up your local database:

1. First, start the services using Docker Compose (this includes Postgres):

   ```bash
   docker compose up
   ```

2. Then run the database setup script to migrate and seed your database:

   ```bash
   yarn db:setup
   ```

   optionally, start fresh with clean tables by running

   ```bash
   yarn db:reset
   ```

This will handle all the necessary database migrations and seeding automatically.

### Usage

This project uses NX as a monorepo framework which allows all interaction to occur at the root of the project. Each project has an identifier which is the name defined in its `package.json`.

To invoke a script for a project, use the command `nx run <project>:<script`, where "project" is the project id and "script" is the specific package.json script you'd like to invoke.

Examples:

- `nx run ict-config-api:watch` - run and watch the ict-config-api service
- `nx run ict-config-api:test` - run tests for ict-config-api
- `nx run ict-config-api:lint` - run the linter for ict-config-api

The root package.json has some additional node scripts defined which can be useful:

- `yarn run test` - run unit tests for every project
- `yarn run lint` - run lint for every project
- `yarn run orders:watch` - helper to run the orders API
- `yarn run clean` - clean all node_modules in root and sub-folders

### Docker Container (preferred)

Docker Compose

- Install Docker Desktop: <https://www.docker.com/products/docker-desktop>
- Run `docker compose up`, includes:
  - API - runs all of the API services in the docker container
  - NGINX - runs a reverse proxy to handle all traffic on 8080 and route to the correct microservice
  - DB - Postgres db for configuration data
  - Redis - Caching for requests

NOTE - If some APIs sporadically fail when starting up you may need to increase your Docker resource limits. This can be done in Docker Desktop > Settings > Resources. Set the "Memory Limit" to at least 16GB.

### Improved Testing Experience (Visual Studio Code)

- To improve creating, running, and debugging unit test, it is suggested to use this extension:

- Name: Mocha Test Explorer
  VS Marketplace Link: <https://marketplace.visualstudio.com/items?itemName=hbenl.vscode-mocha-test-adapter>

- Recommend setting up a multi-root workspace for this repo, otherwise you'll have to include additional settings to get this working. You'll have to set the following in your workspace settings:

```json
"settings": {
    "mochaExplorer.nodeArgv": [
      "--loader=ts-node/esm",
      "--no-warnings=ExperimentalWarning",
      "--experimental-specifier-resolution=node"
    ]
  }
```

### Debugging Locally

There are three main methods of debugging locally:

- Attaching the VS Code debugger to an already running Docker container running with `ENVIRONMENT=local` and debugging enabled
- Running Node and the API directly and attaching the VS Code debugger
- Running tests through Mocha and attaching the VS Code debugger

There are debugging profiles setup for each one of these three methods for each API package in the repo. Going to the "Run and Debug" tab with VS Code will display all of these profiles within the top dropdown.

## Adding Package Dependencies

As we are managing this monorepo using yarn, you will need to use that tool to add any new dependenies on packages you might add.

- In order to add a new dependency across ALL packages in the monorepo:
  - `yarn add <new_package_name>`
- In order to add a new dependency to a SINGLE package in the monorepo:
  - cd to that packages folder
  - `yarn add <new_package_name>`

## Prettier (VSCode)

- Get the prettier extension: <https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode>
- Set format on save:
  - Open the command palette (Windows: CTRL+SHIFT+P, Mac: CMD+SHIFT+P)
  - Type "Preferences: Open Settings (UI)" hit enter
  - Search for "Format On Save"
  - Check the "Editor: Format On Save" box
- Setting prettier as the default formatter
  - Open the command palette
  - Type "Format Document With..." hit enter
  - Select "Configure Default Formatter..."
  - Select "Prettier - Code formatter"

## Terraform Operations

### Pre-requisites

Use the following commands to install Terraform on a Debian-based system:

```bash
# Update and install dependencies
sudo apt update -y && sudo apt upgrade -y
sudo apt install -y software-properties-common gnupg2 curl

# Install Terraform
curl https://apt.releases.hashicorp.com/gpg | gpg --dearmor > hashicorp.gpg
sudo install -o root -g root -m 644 hashicorp.gpg /etc/apt/trusted.gpg.d/
sudo apt-add-repository "deb [arch=$(dpkg --print-architecture)] https://apt.releases.hashicorp.com $(lsb_release -cs) main"
sudo apt install terraform -y
```

Windows documentation: <https://developer.hashicorp.com/terraform/tutorials/aws-get-started/install-cli>

### Planning Terraform Environment

> All projects and resources under an environment

To plan Terraform, navigate to the environments/ directory:

```bash
cd ./environments

# Plan changes using the deployment script
./scripts/terragrunt.sh plan --environment dev
```

### Planning Terraform Resources for a specific environment

```bash
cd ./environments/<ENVIRONMENT>/<resources|project>

terragrunt plan
```

### Applying Terraform

Do not manually apply Terraform. Let CI/CD handle it.
(Unless you have to!)

### CI/CD Pipeline

Merge requests will fail if submitted Terraform code is improperly formatted.

### ENV

This project uses [dotenv-flow](https://www.npmjs.com/package/dotenv-flow) to manage environment variables which allows us to utilize a shared `.env` for _non-sensitive_ values. The `.env` file is stored in git and should not contain any sensitive data.

For private or environment variable overrides, create a `.env.local` file. There is a `.env.local.example` provided which can be copied to your own `.env.local`. Note that your personal env file is git ignored.

Example `.env.local`:

```env
ARTIFACTORY_AUTH_TOKEN=token
REDIS_REQUEST_CACHE_OVERRIDE=false
```

### Request Caching

The API supports request caching using Redis. Each controller defines its own caching strategy using the `cache-middleware` and specifying the minutes length TTL.

Caching is disabled by default for normal development as we typically want to make frequent changes and see them in real-time without any caching. However, request caching can be enabled by running Redis and toggling the `REDIS_REQUEST_CACHE_OVERRIDE` to "true". This should be done in your `.env.local` file.

### Using Bruno to Make API Requests

[Bruno](https://www.usebruno.com/) can be used to make HTTP requests against the API running locally or in deployed environments. To use Bruno:

1. Install Bruno from [usebruno](https://www.usebruno.com/).
2. Configure Custom CA Cert
   1. Download cert from repo /apps/api/certs (ZscalerRootCertificate-Win-2048-SHA256.crt)
   2. Bruno Preferences (bottom left gear)
   3. General > Use custom CA certificate
   4. Select downloaded .crt file
   5. Save
3. In Bruno, click **"Open Collection"**.
4. Choose the Bruno collection inside this repository: `/docs/bruno/ICT_API`.
5. Right-click on your new "ICT API" folder and click **"Settings"**.
6. Choose an Environment (top-right dropdown).
7. Go to the **"Auth"** tab. Click **"Get Access Token"** and log in.

**Note Again:** - If you get an error about "local issuer certificate", go to Bruno Settings and choose "Use custom CA certificate". Choose the cert located in this repo, /apps/api/certs

## Project Structure

This monorepo is organized using Nx with the following structure:

- **apps/api/** - Individual API microservices (config, orders, inventory, etc.)
- **libs/api/** - Shared API libraries and utilities
- **libs/shared/** - Code shared between API and other services
