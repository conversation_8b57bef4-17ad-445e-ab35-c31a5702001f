#!/bin/bash
if [[ $ENVIRONMENT ]]; then
  ENVIRONMENT=$(echo "$ENVIRONMENT" | tr '[:upper:]' '[:lower:]')
fi

# Function to get database credentials from environment variable
get_db_credentials() {
    if [[ -n "$GCP_POSTGRES_SECRET_NAME" ]]; then
        echo "Retrieving database credentials from environment variable..."

        # GCP_POSTGRES_SECRET_NAME now contains the actual JSON content
        DECODED_DATA="$GCP_POSTGRES_SECRET_NAME"

        # Validate JSON structure
        if ! echo "$DECODED_DATA" | jq . > /dev/null 2>&1; then
            echo "Error: Invalid JSON format in GCP_POSTGRES_SECRET_NAME environment variable" >&2
            exit 1
        fi

        # Export all database connection parameters
        export PGHOST=$(echo "$DECODED_DATA" | jq -r '.host')
        export PGPORT=$(echo "$DECODED_DATA" | jq -r '.port')
        export PGUSER=$(echo "$DECODED_DATA" | jq -r '.username')
        export PGPASSWORD=$(echo "$DECODED_DATA" | jq -r '.password')

        # Verify all required fields are present
        if [[ -z "$PGHOST" || -z "$PGPORT" || -z "$PGUSER" || -z "$PGPASSWORD" ]]; then
            echo "Error: Missing required database connection parameters in secret" >&2
            echo "Expected fields: host, port, username, password" >&2
            exit 1
        fi

        echo "Database credentials and connection parameters retrieved and set successfully"
        echo "Connected to host: $PGHOST:$PGPORT as user: $PGUSER"
    else
        echo "Error: GCP_POSTGRES_SECRET_NAME environment variable not set" >&2
        exit 1
    fi
}

if [[ -z "$ENVIRONMENT" || "$ENVIRONMENT" = "local" ]]; then
    echo "Running migrations for local tenants..."
    
    # Set standard PostgreSQL environment variables for local development
    # These are expected by TypeORM and other database clients
    export PGHOST="localhost"
    export PGPORT="5432"
    export PGUSER="$POSTGRES_USERNAME"
    export PGPASSWORD="$POSTGRES_PASSWORD"
    
    TENANT_LIST=$(echo $TENANTS | tr -d '[]"' | tr ',' ' ')
    for TENANT in $TENANT_LIST; do
        # config migrations
        echo "Running config migrations for local tenant: $TENANT"
        psql -h localhost -p 5432 -d $TENANT -U $POSTGRES_USERNAME -c "CREATE SCHEMA IF NOT EXISTS config;"
        CURRENT_TENANT=$TENANT yarn run -T typeorm-ts-node-esm migration:run --dataSource ../../libs/api/ict-api-schema/src/datasources/config/local-migration-config-datasource.ts
        echo "Finished config migrations for local tenant: $TENANT"

        # data-explorer migrations
        echo "Running data-explorer migrations for local tenant: $TENANT"
        psql -h localhost -p 5432 -d $TENANT -U $POSTGRES_USERNAME -c "CREATE SCHEMA IF NOT EXISTS data_explorer;"
        CURRENT_TENANT=$TENANT yarn run -T typeorm-ts-node-esm migration:run --dataSource ../../libs/api/ict-api-schema/src/datasources/data-explorer/local-migration-data-explorer-datasource.ts
        echo "Finished data-explorer migrations for local tenant: $TENANT"

        # process-flow migrations
        echo "Running process-flow migrations for local tenant: $TENANT"
        psql -h localhost -p 5432 -d $TENANT -U $POSTGRES_USERNAME -c "CREATE SCHEMA IF NOT EXISTS process_flow;"
        CURRENT_TENANT=$TENANT yarn run -T typeorm-ts-node-esm migration:run --dataSource ../../libs/api/ict-api-schema/src/datasources/process-flow/local-migration-process-flow-datasource.ts
        echo "Finished process-flow migrations for local tenant: $TENANT"
    done
    
    echo "Finished running migrations for all local tenants."

    echo "Synchronizing settings for local tenants..."
    for TENANT in $TENANT_LIST; do
        SQL_FILE="../../libs/api/ict-api-schema/src/default-data/config/sql/insert-default-settings.sql"
        echo "Synchronizing settings for local tenant: $TENANT"
        PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -p 5432 -d $TENANT -U $POSTGRES_USERNAME -f "$SQL_FILE"
        echo "Finished settings sync for local tenant: $TENANT"
    done
    echo "Finished sync of settings for all local tenants."

    yarn debug
else 
    /usr/app/cloud-sql-proxy --private-ip --port 5432 $CLOUD_SQL_CONN_NAME &
    sleep 5

    # Get database credentials from GCP Secret Manager
    get_db_credentials

    echo "Running migrations for tenants..."
    TENANT_LIST=$(echo $TENANTS | tr -d '[]"' | tr ',' ' ')
    for TENANT in $TENANT_LIST; do
        # config migrations
        echo "Running config migrations for tenant: $TENANT"
        psql -h $PGHOST -p $PGPORT -d $TENANT -U $PGUSER -c "CREATE SCHEMA IF NOT EXISTS config;"
        CURRENT_TENANT=$TENANT yarn typeorm migration:run --dataSource ./build/config/cloud-migration-config-datasource.cjs
        echo "Finished config migrations for tenant: $TENANT"

        # data-explorer migrations
        echo "Running data-explorer migrations for tenant: $TENANT"
        psql -h $PGHOST -p $PGPORT -d $TENANT -U $PGUSER -c "CREATE SCHEMA IF NOT EXISTS data_explorer;"
        CURRENT_TENANT=$TENANT yarn typeorm migration:run --dataSource ./build/data-explorer/cloud-migration-data-explorer-datasource.cjs
        echo "Finished data-explorer migrations for tenant: $TENANT"

        # process-flow migrations
        echo "Running process-flow migrations for tenant: $TENANT"
        psql -h $PGHOST -p $PGPORT -d $TENANT -U $PGUSER -c "CREATE SCHEMA IF NOT EXISTS process_flow;"
        CURRENT_TENANT=$TENANT yarn typeorm migration:run --dataSource ./build/process-flow/cloud-migration-process-flow-datasource.cjs
        echo "Finished process-flow migrations for tenant: $TENANT"
    done
    echo "Finished running migrations for all tenants."

    echo "Synchronizing settings for tenants..."
    for TENANT in $TENANT_LIST; do
        SQL_FILE="./build/insert-default-settings.sql"
        echo "Synchronizing settings for tenant: $TENANT"
        psql -h $PGHOST -p $PGPORT -d $TENANT -U $PGUSER -f "$SQL_FILE"
        echo "Finished settings sync for tenant: $TENANT"
    done
    echo "Finished sync of settings for all tenants."

    echo "Synchronizing metric configs for tenants..."
    for TENANT in $TENANT_LIST; do
        SQL_FILE="./build/insert-default-metric-configs.sql"
        echo "Synchronizing metric configs for tenant: $TENANT"
        psql -h $PGHOST -p $PGPORT -d $TENANT -U $PGUSER -f "$SQL_FILE"
        echo "Finished metric configs sync for tenant: $TENANT"
    done
    echo "Finished sync of metric configs for all tenants."

    yarn start
fi
