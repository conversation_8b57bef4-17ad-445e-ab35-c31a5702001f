import * as sinon from 'sinon';
import * as chai from 'chai';
import chaiAsPromised from 'chai-as-promised';
import axios from 'axios';
import {UsersService} from '../../services/users-service.ts';
import {SecurityRoles} from '@ict/sdk-foundations/types/roles/security-roles.ts';
import {RoleType} from '@ict/sdk-foundations/types/defs/admin-defs.ts';
import {
  UsersListQueryParams,
  Auth0UsersResponse,
  Auth0User,
} from '../../defs/users-list-def.ts';
import {SingleFilterObject} from '@ict/sdk-foundations/types';

const expect = chai.expect;
chai.use(chaiAsPromised);

interface TestLogger {
  info: sinon.SinonStub;
  error: sinon.SinonStub;
  debug: sinon.SinonStub;
}

interface TestContextService {
  userRoles: string[] | undefined;
  userId: string | undefined;
  organization: {
    id: string;
  };
}

interface TestAuthManagementService {
  getManagementApiToken: sinon.SinonStub;
}

interface TestRoleService {
  isInternalUser: sinon.SinonStub;
  isFacilityAdmin: sinon.SinonStub;
  determineRoleType: sinon.SinonStub;
  getAssignableRoles: sinon.SinonStub;
}

describe('UsersService', () => {
  let service: UsersService;
  let loggerStub: TestLogger;
  let contextServiceStub: TestContextService;
  let authManagementServiceStub: TestAuthManagementService;
  let roleServiceStub: TestRoleService;
  let axiosStub: sinon.SinonStub;

  const mockAuth0Users: Auth0User[] = [
    {
      user_id: 'user1',
      email: '<EMAIL>',
      name: 'User One',
      email_verified: true,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
      last_login: '2023-12-01T00:00:00.000Z',
      logins_count: 5,
      roles: ['facility_a'],
      identities: [
        {
          connection: 'Username-Password-Authentication',
          user_id: 'user1',
          provider: 'auth0',
          isSocial: false,
        },
      ],
    },
    {
      user_id: 'user2',
      email: '<EMAIL>',
      name: 'User Two',
      email_verified: false,
      created_at: '2023-02-01T00:00:00.000Z',
      updated_at: '2023-02-01T00:00:00.000Z',
      last_login: '2023-11-01T00:00:00.000Z',
      logins_count: 2,
      roles: ['facility_b_facility_admin'],
      identities: [
        {
          connection: 'google-oauth2',
          user_id: 'google-oauth2|123456',
          provider: 'google-oauth2',
          isSocial: true,
        },
      ],
    },
  ];

  const mockAuth0Response: Auth0UsersResponse = {
    users: mockAuth0Users,
    total: 2,
    start: 0,
    limit: 10,
    length: 2,
  };

  beforeEach(() => {
    // Set up environment variables
    process.env.ADMIN_API_URL = 'https://test.auth0.com';

    // Create stubs
    loggerStub = {
      info: sinon.stub(),
      error: sinon.stub(),
      debug: sinon.stub(),
    };
    contextServiceStub = {
      userRoles: undefined,
      userId: undefined,
      organization: {
        id: 'test-org-id',
      },
    };
    authManagementServiceStub = {
      getManagementApiToken: sinon.stub(),
    };
    roleServiceStub = {
      isInternalUser: sinon.stub(),
      isFacilityAdmin: sinon.stub(),
      determineRoleType: sinon.stub(),
      getAssignableRoles: sinon.stub(),
    };
    axiosStub = sinon.stub(axios, 'get');

    // Set up common default mock behaviors
    authManagementServiceStub.getManagementApiToken.resolves('test-token');
    axiosStub.resolves({data: mockAuth0Response});
    roleServiceStub.getAssignableRoles.resolves([
      {
        name: 'facility_admin',
        displayName: 'Facility Admin',
        description: 'Administrator role for facility',
        roleType: RoleType.ADMIN,
      },
      {
        name: 'facility_user',
        displayName: 'Facility User',
        description: 'User role for facility',
        roleType: RoleType.FACILITY,
      },
    ]);
    roleServiceStub.determineRoleType
      .withArgs('facility_a')
      .returns(RoleType.FACILITY);
    roleServiceStub.determineRoleType
      .withArgs('facility_b_facility_admin')
      .returns(RoleType.ADMIN);

    // Create service instance with stubs
    service = new UsersService();
    (service as unknown as {logger: TestLogger}).logger = loggerStub;
    (
      service as unknown as {contextService: TestContextService}
    ).contextService = contextServiceStub;
    (
      service as unknown as {authManagementService: TestAuthManagementService}
    ).authManagementService = authManagementServiceStub;
    (service as unknown as {roleService: TestRoleService}).roleService =
      roleServiceStub;
  });

  afterEach(() => {
    sinon.restore();
    delete process.env.ADMIN_API_URL;
  });

  describe('getUsersList', () => {
    const baseQueryParams: UsersListQueryParams = {
      page: 0,
      limit: 10,
      sortFields: [],
    };

    it('should throw unauthorized error if user context not found', async () => {
      contextServiceStub.userRoles = undefined;
      contextServiceStub.userId = undefined;

      await expect(service.getUsersList(baseQueryParams)).to.be.rejectedWith(
        'User context not found',
      );
    });

    it('should throw unauthorized error if user roles not found', async () => {
      contextServiceStub.userRoles = undefined;
      contextServiceStub.userId = 'test-user';

      await expect(service.getUsersList(baseQueryParams)).to.be.rejectedWith(
        'User context not found',
      );
    });

    it('should throw unauthorized error if user ID not found', async () => {
      contextServiceStub.userRoles = ['some_role'];
      contextServiceStub.userId = undefined;

      await expect(service.getUsersList(baseQueryParams)).to.be.rejectedWith(
        'User context not found',
      );
    });

    it('should throw forbidden error if user has insufficient permissions', async () => {
      contextServiceStub.userRoles = ['regular_user'];
      contextServiceStub.userId = 'test-user';

      await expect(service.getUsersList(baseQueryParams)).to.be.rejectedWith(
        'Insufficient permissions to view users',
      );
    });

    it('should return users list for internal user (ct_configurator)', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);

      const result = await service.getUsersList(baseQueryParams);

      expect(result).to.deep.equal({
        data: [
          {
            id: 'user1',
            email: '<EMAIL>',
            name: 'User One',
            emailVerified: true,
            createdAt: '2023-01-01T00:00:00.000Z',
            lastLogin: '2023-12-01T00:00:00.000Z',
            loginCount: 5,
            isSocialAuth: false,
          },
          {
            id: 'user2',
            email: '<EMAIL>',
            name: 'User Two',
            emailVerified: false,
            createdAt: '2023-02-01T00:00:00.000Z',
            lastLogin: '2023-11-01T00:00:00.000Z',
            loginCount: 2,
            isSocialAuth: true,
          },
        ],
        metadata: {
          page: 0,
          limit: 10,
          totalResults: 2,
        },
      });

      // Verify organization filter is applied even for admin users
      const axiosCall = axiosStub.getCall(0);
      const queryParam = axiosCall.args[1].params.q;
      expect(queryParam).to.include('organization_id:"test-org-id"');
    });

    it('should return users list for internal user (ct_engineer)', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_ENGINEERS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);

      const result = await service.getUsersList(baseQueryParams);

      expect(result.data).to.have.length(2);
      expect(result.metadata.totalResults).to.equal(2);
    });

    it('should return users list for facility admin', async () => {
      contextServiceStub.userRoles = ['facility_a_facility_admin'];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(false);
      roleServiceStub.isFacilityAdmin.returns(true);

      const result = await service.getUsersList(baseQueryParams);

      expect(result.data).to.have.length(2);
      expect(axiosStub.calledOnce).to.be.true;

      // Check that organization filter and domain restrictions are applied for non-admin users
      const axiosCall = axiosStub.getCall(0);
      const queryParam = axiosCall.args[1].params.q;
      expect(queryParam).to.include('organization_id:"test-org-id"');
      expect(queryParam).to.include('NOT email.domain:"dematic.com"');
      expect(queryParam).to.include('NOT email.domain:"kiongroup.com"');
    });

    it('should filter users by organization ID for all user types', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      contextServiceStub.organization.id = 'specific-org-123';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);

      await service.getUsersList(baseQueryParams);

      const axiosCall = axiosStub.getCall(0);
      const queryParam = axiosCall.args[1].params.q;
      expect(queryParam).to.include('organization_id:"specific-org-123"');
    });

    it('should handle search string parameter', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);

      const queryParamsWithSearch: UsersListQueryParams = {
        ...baseQueryParams,
        searchString: 'test search',
      };

      await service.getUsersList(queryParamsWithSearch);

      const axiosCall = axiosStub.getCall(0);
      const queryParam = axiosCall.args[1].params.q;
      expect(queryParam).to.include('organization_id:"test-org-id"');
      expect(queryParam).to.include('email:*test search*');
      expect(queryParam).to.include('name:*test search*');
    });

    it('should handle sort fields parameter', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);

      const queryParamsWithSort: UsersListQueryParams = {
        ...baseQueryParams,
        sortFields: [{columnName: 'email', isDescending: true}],
      };

      await service.getUsersList(queryParamsWithSort);

      const axiosCall = axiosStub.getCall(0);
      const sortParam = axiosCall.args[1].params.sort;
      expect(sortParam).to.equal('email:-1');
    });

    it('should handle filters parameter', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);

      const queryParamsWithFilters: UsersListQueryParams = {
        ...baseQueryParams,
        filters: {
          type: 'single',
          comparison: 'contains',
          name: 'email',
          value: '<EMAIL>',
        } as SingleFilterObject,
      };

      await service.getUsersList(queryParamsWithFilters);

      const axiosCall = axiosStub.getCall(0);
      const queryParam = axiosCall.args[1].params.q;
      expect(queryParam).to.include('organization_id:"test-org-id"');
      expect(queryParam).to.include('email:*<EMAIL>*');
    });

    it('should throw error if ADMIN_API_URL environment variable not set', async () => {
      delete process.env.ADMIN_API_URL;
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);

      await expect(service.getUsersList(baseQueryParams)).to.be.rejectedWith(
        'ADMIN_API_URL environment variable not set',
      );
    });

    it('should handle Auth0 401 error', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);
      axiosStub.rejects({
        isAxiosError: true,
        response: {
          status: 401,
          data: {error: 'unauthorized'},
        },
      });

      await expect(service.getUsersList(baseQueryParams)).to.be.rejectedWith(
        'Invalid Auth0 credentials',
      );
    });

    it('should handle Auth0 403 error', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);
      axiosStub.rejects({
        isAxiosError: true,
        response: {
          status: 403,
          data: {error: 'forbidden'},
        },
      });

      await expect(service.getUsersList(baseQueryParams)).to.be.rejectedWith(
        'Insufficient permissions for Auth0 Management API',
      );
    });

    it('should handle Auth0 429 rate limit error', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);
      axiosStub.rejects({
        isAxiosError: true,
        response: {
          status: 429,
          data: {error: 'rate_limit_exceeded'},
        },
      });

      await expect(service.getUsersList(baseQueryParams)).to.be.rejectedWith(
        'Too many requests to Auth0. Please try again later.',
      );
    });

    it('should handle generic Auth0 error', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);
      axiosStub.rejects({
        isAxiosError: true,
        response: {
          status: 500,
          data: {error: 'internal_server_error'},
        },
      });

      await expect(service.getUsersList(baseQueryParams)).to.be.rejectedWith(
        'Failed to fetch users from Auth0',
      );
    });

    it('should handle non-axios error', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);
      axiosStub.rejects(new Error('Network error'));

      await expect(service.getUsersList(baseQueryParams)).to.be.rejectedWith(
        'Failed to fetch users from Auth0',
      );
    });

    it('should handle auth management service token error', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);
      authManagementServiceStub.getManagementApiToken.rejects(
        new Error('Token error'),
      );

      await expect(service.getUsersList(baseQueryParams)).to.be.rejectedWith(
        'Failed to retrieve users list',
      );
    });

    it('should handle different sort field mappings', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);

      const testCases = [
        {input: 'name', expected: 'name:1'},
        {input: 'createdAt', expected: 'created_at:1'},
        {input: 'created_at', expected: 'created_at:1'},
        {input: 'lastLogin', expected: 'last_login:1'},
        {input: 'last_login', expected: 'last_login:1'},
        {input: 'loginCount', expected: 'logins_count:1'},
        {input: 'logins_count', expected: 'logins_count:1'},
        {input: 'unsupported', expected: 'email:1'}, // default fallback
      ];

      for (const testCase of testCases) {
        axiosStub.resetHistory();
        const queryParamsWithSort: UsersListQueryParams = {
          ...baseQueryParams,
          sortFields: [{columnName: testCase.input, isDescending: false}],
        };

        await service.getUsersList(queryParamsWithSort);

        const axiosCall = axiosStub.getCall(0);
        const sortParam = axiosCall.args[1].params.sort;
        expect(sortParam).to.equal(testCase.expected);
      }
    });

    it('should handle boolean filters correctly', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.userId = 'test-user';
      roleServiceStub.isInternalUser.returns(true);
      roleServiceStub.isFacilityAdmin.returns(false);

      // Test emailVerified filter
      const emailVerifiedFilter: UsersListQueryParams = {
        ...baseQueryParams,
        filters: {
          type: 'single',
          comparison: 'equals',
          name: 'emailVerified',
          value: true,
        } as any, // Using any to allow boolean value
      };

      await service.getUsersList(emailVerifiedFilter);
      let axiosCall = axiosStub.getCall(0);
      let queryParam = axiosCall.args[1].params.q;
      expect(queryParam).to.include('organization_id:"test-org-id"');
      expect(queryParam).to.include('email_verified:true');

      axiosStub.resetHistory();

      // Test blocked filter
      const blockedFilter: UsersListQueryParams = {
        ...baseQueryParams,
        filters: {
          type: 'single',
          comparison: 'equals',
          name: 'blocked',
          value: false,
        } as any, // Using any to allow boolean value
      };

      await service.getUsersList(blockedFilter);
      axiosCall = axiosStub.getCall(0);
      queryParam = axiosCall.args[1].params.q;
      expect(queryParam).to.include('organization_id:"test-org-id"');
      expect(queryParam).to.include('blocked:false');
    });
  });
});
