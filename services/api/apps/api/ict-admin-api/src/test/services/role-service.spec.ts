import * as sinon from 'sinon';
import * as chai from 'chai';
import chaiAsPromised from 'chai-as-promised';
import axios from 'axios';
import {RoleService} from '../../services/role-service.ts';
import {SecurityRoles} from '@ict/sdk-foundations/types/roles/security-roles.ts';
import {RoleType} from '@ict/sdk-foundations/types/defs/admin-defs.ts';
import {AppConfigSettingSource} from '@ict/sdk-foundations/types/index.ts';

const expect = chai.expect;
chai.use(chaiAsPromised);

interface TestLogger {
  info: sinon.SinonStub;
  error: sinon.SinonStub;
  warn: sinon.SinonStub;
  debug: sinon.SinonStub;
}

interface TestContextService {
  userRoles: string[];
  facilityMaps?: any[];
}

interface TestConfigStore {
  findSpecificSettingValue: sinon.SinonStub;
}

describe('RoleService', () => {
  let service: RoleService;
  let loggerStub: TestLogger;
  let contextServiceStub: TestContextService;
  let configStoreStub: TestConfigStore;

  const mockFacilityMaps = [
    {
      id: 'facility_a',
      name: 'Facility A',
      dataset: 'dataset_a',
    },
    {
      id: 'facility_b',
      name: 'Facility B',
      dataset: 'dataset_b',
    },
  ];

  beforeEach(() => {
    // Create stubs
    loggerStub = {
      info: sinon.stub(),
      error: sinon.stub(),
      warn: sinon.stub(),
      debug: sinon.stub(),
    };
    contextServiceStub = {
      userRoles: [],
      facilityMaps: [],
    };
    configStoreStub = {
      findSpecificSettingValue: sinon.stub(),
    };

    // Create service instance with stubs
    service = new RoleService();
    (service as unknown as {logger: TestLogger}).logger = loggerStub;
    (
      service as unknown as {contextService: TestContextService}
    ).contextService = contextServiceStub;
    (service as unknown as {configStore: TestConfigStore}).configStore =
      configStoreStub;
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAssignableRoles', () => {
    it('should throw unauthorized error if user roles not found', async () => {
      contextServiceStub.userRoles = [];

      await expect(service.getAssignableRoles()).to.be.rejectedWith(
        'User roles not found in context',
      );
    });

    it('should return internal and all facility roles for internal users (ct_configurator)', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.facilityMaps = mockFacilityMaps;

      const roles = await service.getAssignableRoles();

      expect(roles).to.have.length(6); // 2 internal + 4 facility roles (2 facilities * 2 roles each)

      // Check internal roles
      const internalRoles = roles.filter(r => r.roleType === RoleType.INTERNAL);
      expect(internalRoles).to.have.length(2);
      expect(internalRoles.map(r => r.name)).to.include.members([
        SecurityRoles.CT_CONFIGURATORS,
        SecurityRoles.CT_ENGINEERS,
      ]);

      // Check facility roles
      const facilityRoles = roles.filter(r => r.roleType === RoleType.FACILITY);
      expect(facilityRoles).to.have.length(2);
      expect(facilityRoles.map(r => r.name)).to.include.members([
        'facility_a',
        'facility_b',
      ]);

      // Check admin roles
      const adminRoles = roles.filter(r => r.roleType === RoleType.ADMIN);
      expect(adminRoles).to.have.length(2);
      expect(adminRoles.map(r => r.name)).to.include.members([
        'facility_a_facility_admin',
        'facility_b_facility_admin',
      ]);
    });

    it('should return internal and all facility roles for internal users (ct_engineer)', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_ENGINEERS];
      contextServiceStub.facilityMaps = mockFacilityMaps;

      const roles = await service.getAssignableRoles();

      expect(roles).to.have.length(6); // 2 internal + 4 facility roles

      const internalRoles = roles.filter(r => r.roleType === RoleType.INTERNAL);
      expect(internalRoles).to.have.length(2);
    });

    it('should return only facility roles for facilities the user administers', async () => {
      contextServiceStub.userRoles = ['facility_a_facility_admin'];
      contextServiceStub.facilityMaps = mockFacilityMaps;

      const roles = await service.getAssignableRoles();

      expect(roles).to.have.length(2); // Only facility_a roles
      expect(roles.map(r => r.name)).to.include.members([
        'facility_a',
        'facility_a_facility_admin',
      ]);
    });

    it('should return empty array for regular facility users (no admin privileges)', async () => {
      contextServiceStub.userRoles = ['facility_a']; // Regular facility access, not admin
      contextServiceStub.facilityMaps = mockFacilityMaps;

      const roles = await service.getAssignableRoles();

      expect(roles).to.have.length(0);
    });

    it('should return roles for multiple facilities if user is admin of multiple', async () => {
      contextServiceStub.userRoles = [
        'facility_a_facility_admin',
        'facility_b_facility_admin',
      ];
      contextServiceStub.facilityMaps = mockFacilityMaps;

      const roles = await service.getAssignableRoles();

      expect(roles).to.have.length(4); // 2 facilities * 2 roles each
      expect(roles.map(r => r.name)).to.include.members([
        'facility_a',
        'facility_a_facility_admin',
        'facility_b',
        'facility_b_facility_admin',
      ]);
    });

    it('should load facility maps from config store if not in context', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.facilityMaps = undefined;
      configStoreStub.findSpecificSettingValue.resolves({
        value: mockFacilityMaps,
      });

      const roles = await service.getAssignableRoles();

      expect(
        configStoreStub.findSpecificSettingValue.calledWith(
          AppConfigSettingSource.tenant,
          undefined,
          'facility-maps',
        ),
      ).to.be.true;
      expect(roles).to.have.length(6);
    });

    it('should fall back to default settings if tenant settings not found', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.facilityMaps = undefined;
      configStoreStub.findSpecificSettingValue
        .onFirstCall()
        .resolves(null) // tenant call returns null
        .onSecondCall()
        .resolves({value: mockFacilityMaps}); // default call returns data

      const roles = await service.getAssignableRoles();

      expect(configStoreStub.findSpecificSettingValue.calledTwice).to.be.true;
      expect(
        configStoreStub.findSpecificSettingValue.secondCall.calledWith(
          AppConfigSettingSource.default,
          undefined,
          'facility-maps',
        ),
      ).to.be.true;
      expect(roles).to.have.length(6);
    });

    it('should handle empty facility maps gracefully', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.facilityMaps = [];

      const roles = await service.getAssignableRoles();

      expect(roles).to.have.length(2); // Only internal roles
      const internalRoles = roles.filter(r => r.roleType === RoleType.INTERNAL);
      expect(internalRoles).to.have.length(2);
    });

    it('should throw error if config store fails', async () => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      contextServiceStub.facilityMaps = undefined;
      configStoreStub.findSpecificSettingValue.rejects(
        new Error('Config store error'),
      );

      await expect(service.getAssignableRoles()).to.be.rejectedWith(
        'Failed to load facility configuration',
      );
    });
  });

  describe('getRoleIdByName', () => {
    let authManagementServiceStub: any;

    beforeEach(() => {
      authManagementServiceStub = {
        getManagementApiToken: sinon.stub().resolves('mock-token'),
      };
      (service as any).authManagementService = authManagementServiceStub;
      process.env.ADMIN_API_URL = 'https://mock-auth0.com';
    });

    afterEach(() => {
      delete process.env.ADMIN_API_URL;
    });

    it('should return Auth0 role ID when role name is found', async () => {
      const mockAxiosGet = sinon.stub().resolves({
        data: [
          {id: 'auth0-role-id-123', name: 'facility_a'},
          {id: 'auth0-role-id-456', name: 'ct_configurator'},
        ],
      });

      // Mock axios for this test
      sinon.stub(axios, 'get').callsFake(mockAxiosGet);

      const roleId = await (service as any).getRoleIdByName('facility_a');

      expect(roleId).to.equal('auth0-role-id-123');
      expect(mockAxiosGet.calledOnce).to.be.true;
    });

    it('should throw not found error when role name does not exist', async () => {
      const mockAxiosGet = sinon.stub().resolves({
        data: [{id: 'auth0-role-id-456', name: 'ct_configurator'}],
      });

      sinon.stub(axios, 'get').callsFake(mockAxiosGet);

      await expect(
        (service as any).getRoleIdByName('nonexistent_role'),
      ).to.be.rejectedWith('Role not found: nonexistent_role');
    });
  });

  describe('assignUserOrganizationRoles', () => {
    beforeEach(() => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      (service as any).contextService.userId = 'current-user-id';
      (service as any).contextService.organization = {id: 'org-123'};
    });

    it('should assign roles using role names', async () => {
      contextServiceStub.facilityMaps = mockFacilityMaps;

      const getRoleIdByNameStub = sinon.stub().resolves('auth0-role-id-123');
      const assignAuth0RolesStub = sinon.stub().resolves();

      (service as any).getRoleIdByName = getRoleIdByNameStub;
      (service as any).assignAuth0OrganizationMemberRoles =
        assignAuth0RolesStub;

      const result = await service.assignUserOrganizationRoles('user-123', [
        'facility_a',
      ]);

      expect(result.success).to.be.true;
      expect(getRoleIdByNameStub.calledWith('facility_a')).to.be.true;
      expect(
        assignAuth0RolesStub.calledWith('user-123', 'org-123', [
          'auth0-role-id-123',
        ]),
      ).to.be.true;
    });
  });

  describe('removeUserOrganizationRole', () => {
    beforeEach(() => {
      contextServiceStub.userRoles = [SecurityRoles.CT_CONFIGURATORS];
      (service as any).contextService.userId = 'current-user-id';
      (service as any).contextService.organization = {id: 'org-123'};
    });

    it('should remove role using role name', async () => {
      contextServiceStub.facilityMaps = mockFacilityMaps;

      const getRoleIdByNameStub = sinon.stub().resolves('auth0-role-id-123');
      const removeAuth0RoleStub = sinon.stub().resolves();

      (service as any).getRoleIdByName = getRoleIdByNameStub;
      (service as any).removeAuth0OrganizationMemberRole = removeAuth0RoleStub;

      const result = await service.removeUserOrganizationRole(
        'user-123',
        'facility_a',
      );

      expect(result.success).to.be.true;
      expect(getRoleIdByNameStub.calledWith('facility_a')).to.be.true;
      expect(
        removeAuth0RoleStub.calledWith(
          'user-123',
          'org-123',
          'auth0-role-id-123',
        ),
      ).to.be.true;
    });
  });
});
