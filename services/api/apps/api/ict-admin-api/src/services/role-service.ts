import {
  DiService,
  ContextService,
  <PERSON>ct<PERSON>rror,
  <PERSON><PERSON><PERSON><PERSON>,
  Container,
  ConfigStore,
  HttpStatusCodes,
} from 'ict-api-foundations';
import {FacilityMap} from '@ict/sdk-foundations/types';
import {AppConfigSettingSource} from '@ict/sdk-foundations/types/index.ts';
import {SecurityRoles} from '@ict/sdk-foundations/types/roles/security-roles.ts';
import {Role, RoleType} from '@ict/sdk-foundations/types/defs/admin-defs.ts';
import axios, {AxiosResponse} from 'axios';
import {
  Auth0Role,
  AssignRoleResponse,
  RemoveRoleResponse,
} from '../defs/users-list-def.ts';
import {AuthManagementService} from './auth-management-service.ts';

@DiService()
export class RoleService {
  private logger: WinstonLogger;
  private contextService: ContextService;
  private configStore: ConfigStore;
  private authManagementService: AuthManagementService;

  constructor() {
    this.logger = Container.get(WinstonLogger);
    this.contextService = Container.get(ContextService);
    this.configStore = Container.get(ConfigStore);
    this.authManagementService = Container.get(AuthManagementService);
    this.logger.info('RoleService initialized');
  }

  /**
   * Gets the roles that the current user can assign to other users
   */
  public async getAssignableRoles(): Promise<Role[]> {
    const userRoles = this.contextService.userRoles;

    if (!userRoles || userRoles.length === 0) {
      throw IctError.unauthorized('User roles not found in context');
    }

    this.logger.info('Getting assignable roles for user', {
      userRoles,
    });

    const roles: Role[] = [];

    // Check if user is internal (has ct_configurator or ct_engineer)
    const isInternalUser = this.isInternalUser(userRoles);

    if (isInternalUser) {
      // Internal users can assign internal roles
      roles.push(...this.getInternalRoles());

      // Internal users can also assign all facility roles
      const facilityRoles = await this.getAllFacilityRoles();
      roles.push(...facilityRoles);
    } else {
      // Non-internal users can only assign roles for facilities they have access to
      const accessibleFacilityRoles =
        await this.getAccessibleFacilityRoles(userRoles);
      roles.push(...accessibleFacilityRoles);
    }

    this.logger.info('Returning assignable roles', {
      roleCount: roles.length,
      roles: roles.map(r => ({name: r.name, roleType: r.roleType})),
    });

    return roles;
  }

  /**
   * Checks if the user has internal roles (ct_configurator or ct_engineer)
   */
  public isInternalUser(userRoles: string[]): boolean {
    return (
      userRoles.includes(SecurityRoles.CT_CONFIGURATORS) ||
      userRoles.includes(SecurityRoles.CT_ENGINEERS)
    );
  }

  /**
   * Gets the internal roles that can be assigned
   */
  private getInternalRoles(): Role[] {
    return [
      {
        name: SecurityRoles.CT_CONFIGURATORS,
        displayName: 'Control Tower Configurator',
        description: 'Can configure Control Tower settings',
        roleType: RoleType.INTERNAL,
      },
      {
        name: SecurityRoles.CT_ENGINEERS,
        displayName: 'Control Tower Engineer',
        description: 'Engineering access to Control Tower',
        roleType: RoleType.INTERNAL,
      },
    ];
  }

  /**
   * Gets all facility roles from the facility maps
   */
  private async getAllFacilityRoles(): Promise<Role[]> {
    const facilityMaps = await this.getFacilityMaps();
    const roles: Role[] = [];

    for (const facilityMap of facilityMaps) {
      // Add the facility role
      roles.push({
        name: facilityMap.id,
        displayName: facilityMap.name || facilityMap.id,
        description: `Access to ${facilityMap.name || facilityMap.id} facility`,
        roleType: RoleType.FACILITY,
      });

      // Add the facility admin role
      roles.push({
        name: `${facilityMap.id}_facility_admin`,
        displayName: `${facilityMap.name || facilityMap.id} Facility Admin`,
        description: `Admin access to ${facilityMap.name || facilityMap.id} facility`,
        roleType: RoleType.ADMIN,
      });
    }

    return roles;
  }

  /**
   * Gets facility roles that the user can assign (only for facilities they admin)
   */
  private async getAccessibleFacilityRoles(
    userRoles: string[],
  ): Promise<Role[]> {
    const facilityMaps = await this.getFacilityMaps();
    const roles: Role[] = [];

    for (const facilityMap of facilityMaps) {
      // Check if user is admin of this facility
      const isFacilityAdmin = userRoles.includes(
        `${facilityMap.id}_facility_admin`,
      );

      // Only facility admins can assign roles for their facilities
      if (isFacilityAdmin) {
        // User can assign the facility role
        roles.push({
          name: facilityMap.id,
          displayName: facilityMap.name || facilityMap.id,
          description: `Access to ${facilityMap.name || facilityMap.id} facility`,
          roleType: RoleType.FACILITY,
        });

        // Facility admins can also assign facility admin role
        roles.push({
          name: `${facilityMap.id}_facility_admin`,
          displayName: `${facilityMap.name || facilityMap.id} Facility Admin`,
          description: `Admin access to ${facilityMap.name || facilityMap.id} facility`,
          roleType: RoleType.ADMIN,
        });
      }
    }

    return roles;
  }

  /**
   * Gets facility maps from config store
   */
  private async getFacilityMaps(): Promise<FacilityMap[]> {
    try {
      // First try to get from context (might already be loaded)
      let facilityMaps = this.contextService.facilityMaps;

      if (!facilityMaps) {
        // Try to load from tenant settings
        let facilityMapsSetting =
          await this.configStore.findSpecificSettingValue(
            AppConfigSettingSource.tenant,
            undefined,
            'facility-maps',
          );

        if (!facilityMapsSetting) {
          // Fall back to default settings
          facilityMapsSetting = await this.configStore.findSpecificSettingValue(
            AppConfigSettingSource.default,
            undefined,
            'facility-maps',
          );
        }

        if (facilityMapsSetting) {
          facilityMaps = facilityMapsSetting.value as FacilityMap[];
        }
      }

      if (!facilityMaps) {
        this.logger.warn('No facility maps found in config store');
        return [];
      }

      return facilityMaps;
    } catch (error) {
      this.logger.error('Failed to load facility maps from config store', {
        error,
      });
      throw IctError.internalServerError(
        'Failed to load facility configuration',
      );
    }
  }

  /**
   * Assigns organization member roles to a user in Auth0
   */
  public async assignUserOrganizationRoles(
    userId: string,
    roleNames: string[],
  ): Promise<AssignRoleResponse> {
    try {
      const userRoles = this.contextService.userRoles;
      const currentUserId = this.contextService.userId;
      const organizationId = this.contextService.organization.id;

      if (!userRoles || !currentUserId) {
        throw IctError.unauthorized('User context not found');
      }

      const isAdmin = this.isInternalUser(userRoles);
      const isFacilityAdmin = this.isFacilityAdmin(userRoles);

      if (!isAdmin && !isFacilityAdmin) {
        throw IctError.forbidden('Insufficient permissions to assign roles');
      }

      // Validate that the current user can assign these roles
      const assignableRoles = await this.getAssignableRoles();
      const assignableRoleNames = assignableRoles.map(role => role.name);

      const invalidRoles = roleNames.filter(
        roleName => !assignableRoleNames.includes(roleName),
      );

      if (invalidRoles.length > 0) {
        throw IctError.forbidden(
          `Cannot assign roles: ${invalidRoles.join(', ')}`,
        );
      }

      this.logger.info('Assigning organization member roles', {
        currentUserId,
        targetUserId: userId,
        roleNames,
        organizationId,
      });

      // Lookup Auth0 role IDs for the role names
      const auth0RoleIds = await Promise.all(
        roleNames.map(roleName => this.getRoleIdByName(roleName)),
      );

      // Assign roles via Auth0 Management API using the actual Auth0 role IDs
      await this.assignAuth0OrganizationMemberRoles(
        userId,
        organizationId,
        auth0RoleIds,
      );

      // Fetch the updated role objects to return
      const assignedRoles = assignableRoles.filter(role =>
        roleNames.includes(role.name),
      );

      return {
        success: true,
        message: `Successfully assigned ${roleNames.length} role(s)`,
        assignedRoles,
      };
    } catch (error) {
      this.logger.error('Failed to assign user organization roles', {
        userId,
        roleNames,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
        },
      });

      if (error instanceof IctError) {
        throw error;
      }

      throw IctError.internalServerError('Failed to assign user roles');
    }
  }

  /**
   * Removes an organization member role from a user in Auth0
   */
  public async removeUserOrganizationRole(
    userId: string,
    roleName: string,
  ): Promise<RemoveRoleResponse> {
    try {
      const userRoles = this.contextService.userRoles;
      const currentUserId = this.contextService.userId;
      const organizationId = this.contextService.organization.id;

      if (!userRoles || !currentUserId) {
        throw IctError.unauthorized('User context not found');
      }

      const isAdmin = this.isInternalUser(userRoles);
      const isFacilityAdmin = this.isFacilityAdmin(userRoles);

      if (!isAdmin && !isFacilityAdmin) {
        throw IctError.forbidden('Insufficient permissions to remove roles');
      }

      // Validate that the current user can remove this role
      const assignableRoles = await this.getAssignableRoles();
      const canRemoveRole = assignableRoles.some(
        role => role.name === roleName,
      );

      if (!canRemoveRole) {
        throw IctError.forbidden(`Cannot remove role: ${roleName}`);
      }

      this.logger.info('Removing organization member role', {
        currentUserId,
        targetUserId: userId,
        roleName,
        organizationId,
      });

      // Lookup Auth0 role ID for the role name
      const auth0RoleId = await this.getRoleIdByName(roleName);

      // Remove role via Auth0 Management API using the actual Auth0 role ID
      await this.removeAuth0OrganizationMemberRole(
        userId,
        organizationId,
        auth0RoleId,
      );

      return {
        success: true,
        message: `Successfully removed role: ${roleName}`,
      };
    } catch (error) {
      this.logger.error('Failed to remove user organization role', {
        userId,
        roleName,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
        },
      });

      if (error instanceof IctError) {
        throw error;
      }

      throw IctError.internalServerError('Failed to remove user role');
    }
  }

  /**
   * Fetches organization member roles for a user from Auth0
   */
  public async fetchAuth0UserOrganizationRoles(
    userId: string,
    organizationId: string,
  ): Promise<Auth0Role[]> {
    const authDomain = process.env.ADMIN_API_URL;
    if (!authDomain) {
      throw IctError.internalServerError(
        'ADMIN_API_URL environment variable not set',
      );
    }

    const token = await this.authManagementService.getManagementApiToken();
    const rolesUrl = `${authDomain}/api/v2/organizations/${encodeURIComponent(organizationId)}/members/${encodeURIComponent(userId)}/roles`;

    this.logger.debug('Fetching user organization roles from Auth0', {
      url: rolesUrl,
      userId,
      organizationId,
    });

    try {
      const response: AxiosResponse<Auth0Role[]> = await axios.get(rolesUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to fetch user organization roles from Auth0', {
        userId,
        organizationId,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          status: axios.isAxiosError(error)
            ? error.response?.status
            : undefined,
          data: axios.isAxiosError(error) ? error.response?.data : undefined,
        },
      });

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw IctError.unauthorized('Invalid Auth0 credentials');
        }
        if (error.response?.status === 403) {
          throw IctError.forbidden(
            'Insufficient permissions for Auth0 Management API',
          );
        }
        if (error.response?.status === 404) {
          throw IctError.notFound('User not found in organization');
        }
        if (error.response?.status === 429) {
          throw new IctError(
            HttpStatusCodes.TOO_MANY_REQUESTS,
            'Too many requests to Auth0. Please try again later.',
            error.response.data,
          );
        }
      }

      throw IctError.internalServerError(
        'Failed to fetch user organization roles from Auth0',
      );
    }
  }

  /**
   * Determines the role type based on role name
   */
  public determineRoleType(roleName: string): RoleType {
    if (
      roleName === SecurityRoles.CT_ENGINEERS ||
      roleName === SecurityRoles.CT_CONFIGURATORS
    ) {
      return RoleType.INTERNAL;
    }
    if (roleName.endsWith('_facility_admin')) {
      return RoleType.ADMIN;
    }
    return RoleType.FACILITY;
  }

  /**
   * Checks if user has facility admin permissions
   */
  public isFacilityAdmin(userRoles: string[]): boolean {
    return userRoles.some(role => role.endsWith('_facility_admin'));
  }

  /**
   * Assigns organization member roles via Auth0 Management API
   */
  private async assignAuth0OrganizationMemberRoles(
    userId: string,
    organizationId: string,
    roleIds: string[],
  ): Promise<void> {
    const authDomain = process.env.ADMIN_API_URL;
    if (!authDomain) {
      throw IctError.internalServerError(
        'ADMIN_API_URL environment variable not set',
      );
    }

    const token = await this.authManagementService.getManagementApiToken();
    const assignUrl = `${authDomain}/api/v2/organizations/${encodeURIComponent(organizationId)}/members/${encodeURIComponent(userId)}/roles`;

    this.logger.debug('Assigning user organization roles via Auth0', {
      url: assignUrl,
      userId,
      organizationId,
      roleIds,
    });

    try {
      await axios.post(
        assignUrl,
        {
          roles: roleIds,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );
    } catch (error) {
      this.logger.error('Failed to assign user organization roles via Auth0', {
        userId,
        organizationId,
        roleIds,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          status: axios.isAxiosError(error)
            ? error.response?.status
            : undefined,
          data: axios.isAxiosError(error) ? error.response?.data : undefined,
        },
      });

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw IctError.unauthorized('Invalid Auth0 credentials');
        }
        if (error.response?.status === 403) {
          throw IctError.forbidden(
            'Insufficient permissions for Auth0 Management API',
          );
        }
        if (error.response?.status === 404) {
          throw IctError.notFound('User or organization not found');
        }
        if (error.response?.status === 429) {
          throw new IctError(
            HttpStatusCodes.TOO_MANY_REQUESTS,
            'Too many requests to Auth0. Please try again later.',
            error.response.data,
          );
        }
      }

      throw IctError.internalServerError(
        'Failed to assign user organization roles via Auth0',
      );
    }
  }

  /**
   * Removes an organization member role via Auth0 Management API
   */
  private async removeAuth0OrganizationMemberRole(
    userId: string,
    organizationId: string,
    roleId: string,
  ): Promise<void> {
    const authDomain = process.env.ADMIN_API_URL;
    if (!authDomain) {
      throw IctError.internalServerError(
        'ADMIN_API_URL environment variable not set',
      );
    }

    const token = await this.authManagementService.getManagementApiToken();
    const removeUrl = `${authDomain}/api/v2/organizations/${encodeURIComponent(organizationId)}/members/${encodeURIComponent(userId)}/roles`;

    this.logger.debug('Removing user organization role via Auth0', {
      url: removeUrl,
      userId,
      organizationId,
      roleId,
    });

    try {
      await axios.delete(removeUrl, {
        data: {
          roles: [roleId],
        },
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      this.logger.error('Failed to remove user organization role via Auth0', {
        userId,
        organizationId,
        roleId,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          status: axios.isAxiosError(error)
            ? error.response?.status
            : undefined,
          data: axios.isAxiosError(error) ? error.response?.data : undefined,
        },
      });

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw IctError.unauthorized('Invalid Auth0 credentials');
        }
        if (error.response?.status === 403) {
          throw IctError.forbidden(
            'Insufficient permissions for Auth0 Management API',
          );
        }
        if (error.response?.status === 404) {
          throw IctError.notFound('User, organization, or role not found');
        }
        if (error.response?.status === 429) {
          throw new IctError(
            HttpStatusCodes.TOO_MANY_REQUESTS,
            'Too many requests to Auth0. Please try again later.',
            error.response.data,
          );
        }
      }

      throw IctError.internalServerError(
        'Failed to remove user organization role via Auth0',
      );
    }
  }

  /**
   * Looks up Auth0 role ID by role name
   */
  private async getRoleIdByName(roleName: string): Promise<string> {
    const authDomain = process.env.ADMIN_API_URL;
    if (!authDomain) {
      throw IctError.internalServerError(
        'ADMIN_API_URL environment variable not set',
      );
    }

    const token = await this.authManagementService.getManagementApiToken();

    this.logger.debug('Looking up Auth0 role ID by name', {
      roleName,
    });

    try {
      const response: AxiosResponse<Auth0Role[]> = await axios.get(
        `${authDomain}/api/v2/roles`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          params: {
            name_filter: roleName,
          },
        },
      );

      const role = response.data.find(r => r.name === roleName);
      if (!role) {
        throw IctError.notFound(`Role not found: ${roleName}`);
      }

      return role.id;
    } catch (error) {
      this.logger.error('Failed to lookup Auth0 role ID by name', {
        roleName,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          status: axios.isAxiosError(error)
            ? error.response?.status
            : undefined,
          data: axios.isAxiosError(error) ? error.response?.data : undefined,
        },
      });

      if (error instanceof IctError) {
        throw error;
      }

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw IctError.unauthorized('Invalid Auth0 credentials');
        }
        if (error.response?.status === 403) {
          throw IctError.forbidden(
            'Insufficient permissions for Auth0 Management API',
          );
        }
        if (error.response?.status === 429) {
          throw new IctError(
            HttpStatusCodes.TOO_MANY_REQUESTS,
            'Too many requests to Auth0. Please try again later.',
            error.response.data,
          );
        }
      }

      throw IctError.internalServerError('Failed to lookup Auth0 role ID');
    }
  }
}
