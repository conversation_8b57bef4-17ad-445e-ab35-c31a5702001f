import {
  Container,
  ContextService,
  ProtectedRouteMiddleware,
  systemAvailabilityDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';

@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [systemAvailabilityDatabase()],
  }),
])
@Route('availability')
export class AvailabilityController extends Controller {
  @SuccessResponse('200')
  @Get('/test')
  @OperationId('Test')
  @Tags('test')
  public async test(): Promise<{now: string}> {
    const contextService = Container.get(ContextService);

    const results = await contextService.dbProvider.pgPostgres.query(
      'select now() as now',
    );

    if (results.rows.length === 0) {
      throw new Error('No results found');
    }
    return {now: `${results.rows[0].now}`};
  }
}
