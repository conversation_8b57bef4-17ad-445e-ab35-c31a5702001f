import 'reflect-metadata';
import express from 'express';
import {
  ApiMiddleware,
  Container,
  EnvironmentService,
  WinstonLogger,
} from 'ict-api-foundations';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../build/routes.ts';

const app = express();

// setup middlewares
app.use(
  express.urlencoded({
    extended: true,
  })
);
app.use(express.json());
ApiMiddleware.applyApiDefaultMiddlewares(app);
RegisterRoutes(app);

ApiMiddleware.applyErrorMiddlewares(app);
const logger = Container.get(WinstonLogger);

const envService = Container.get(EnvironmentService);
const port = envService.ports.availability || 8080;
app.listen(port, () =>
  logger.info(`Availability API listening on local port ${port}.`)
);
