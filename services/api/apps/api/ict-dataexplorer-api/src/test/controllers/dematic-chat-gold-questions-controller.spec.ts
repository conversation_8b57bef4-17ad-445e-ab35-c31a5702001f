import {expect} from 'chai';
import {Container, appSetup} from 'ict-api-foundations';
import sinon from 'sinon';
import request from 'supertest';
import {HttpStatusCode} from 'axios';
import {
  DematicChatGoldQuestions,
  DematicChatGoldQuestionsData,
} from '../../defs/dematic-chat-gold-questions-def.ts';
import {DataExplorerService} from '../../services/dataexplorer-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';

describe('DematicChatGoldQuestionsController', () => {
  const app = appSetup(RegisterRoutes);

  /** Stub instance for the DataExplorer Service. */
  let dataExplorerServiceStub: sinon.SinonStubbedInstance<DataExplorerService>;

  beforeEach(() => {
    dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
    Container.set(DataExplorerService, dataExplorerServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getDematicChatGoldQuestions', () => {
    let mockServiceResponse: DematicChatGoldQuestionsData[];
    let mockGoldQuestionsData: DematicChatGoldQuestions;

    beforeEach(() => {
      mockServiceResponse = [
        {
          prompt: 'What are the most efficient pick locations?',
        },
        {
          prompt: 'How can we optimize inventory distribution?',
        },
      ];
      mockGoldQuestionsData = {
        goldQuestions: mockServiceResponse,
      };
      dataExplorerServiceStub.getGoldQuestions.resolves(mockServiceResponse);
    });

    it('should return the correct data', async () => {
      const response = await request(app).get('/dematic-chat/gold-questions');
      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockGoldQuestionsData);
    });

    it('should return an error response when an error is thrown', async () => {
      const theError = new Error('This error should be handled');
      dataExplorerServiceStub.getGoldQuestions.throws(theError);
      const response = await request(app).get('/dematic-chat/gold-questions');

      expect(response.body.status).to.equal(HttpStatusCode.InternalServerError);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });
});
