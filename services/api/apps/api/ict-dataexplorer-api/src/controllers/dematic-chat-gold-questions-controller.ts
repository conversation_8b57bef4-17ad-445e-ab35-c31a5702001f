import {
  Container,
  DatabaseTypes,
  PostgresDatabaseOptions,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {EntityTypes} from 'ict-api-schema';
import {
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {
  DematicChatGoldQuestions,
  DematicChatGoldQuestionsData,
} from '../defs/dematic-chat-gold-questions-def.ts';
import {DataExplorerService} from '../services/dataexplorer-service.ts';

const dbOptions: PostgresDatabaseOptions = {
  entityType: EntityTypes.DataExplorer,
};

@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      {
        type: DatabaseTypes.Postgres,
        options: dbOptions,
      },
    ],
  }),
])
@Route('/dematic-chat/gold-questions')
export class DematicChatGoldQuestionsController {
  @Example<DematicChatGoldQuestionsData[]>([
    {prompt: "What's a good gold question for dematic chat?"},
  ])
  @SuccessResponse('200')
  @Get()
  @OperationId('GetDematicChatGoldQuestions')
  @Tags('dematic-chat')
  public async getDematicChatGoldQuestions(): Promise<DematicChatGoldQuestions> {
    const dataExplorerService = Container.get(DataExplorerService);
    const goldQuestions = await dataExplorerService.getGoldQuestions();
    return {goldQuestions};
  }
}
