# Running `ict-metric-processor` Locally

## Prerequisites

1. Install Dependencies:

   - Ensure you have poetry installed:
     `brew install poetry`
   - Install using poetry
     `poetry install --with docs`
   - Black Formatter for python files: <https://marketplace.visualstudio.com/items?itemName=ms-python.black-formatter>

2. Install Docker.

3. Create a `.env` file in the `ict-metric-processor` directory to store environment variables that the application will use.

   - Open a text editor and create a new file named `.env` in the current directory.
   - Copy and paste the values from `.env.example` to your `.env` file. The connection information for Redis and Neo4J should match the default values but you can modify if needed.
   - In your `.env` file, make sure of the following:
     1. Set `REDIS_HOST` to `redis`.
     2. Ensure `NEO4J_URI` is set to `bolt://neo4j:7687`.
     3. Set `NEO4J_USERNAME` TO `neo4j`.
     4. Set `NEO4J_PASSWORD` to any non-empty string other than `neo4j`.

4. Docker Compose Configuration:\*\*
   - The `docker-compose.yml` file is located in the root directory of `ict-metric-processor`. This file is used to build and run a Docker container that includes Redis, Neo4j, and the metric processor.

### Generate Documentation

1. Serve the documentation on localhost
   `mkdocs serve`
2. To learn the configuration schema, navigate to:
   `/api/configurations`

## Run Locally in a Docker Container

For development purposes, we run this project using Docker Compose. We have sample testing data in the `/src/tests/data/` that we send to the Metric Processor container using `curl`. The data sent by `curl` consists of an array of `1-N fact messages`, and some meta data like `Tenant`, `Facility`, and `Fact Type`.

`main.py` does the work of receiving those message batches, and then parsing and sending each message on to the Metric Processor. Similar to what the `dev-ict-d-etl-insights_pubsub_to_bigquery` CloudRun does in GCP.

The Metric Processor then processes each message, and updates Neo4j and/or Redis accordingly.

### Steps to Run

1. **Build and Start the Docker Containers:**

   - Navigate to the root directory of `ict-metric-processor`.
   - Run the following command to build and start the containers:
     `docker compose up`
     - If you'd like it to watch for updates, add the watch flag `docker compose up --watch`
   - This will create 3 containers. The Metric Processor, a Redis server, and a Neo4j DB.

2. **Run Metric Processor with Test Data**:

   - Run all test data through the metric processor using the script in `tools/run_all_data.sh`:

     ```bash
     ./tools/run_all_data.sh
     ```

   - This script will run test data for various scenarios:
     - Miniload Test
     - Multishuttle Test
     - Shuttle Test
     - Workstations Test
     - Connection Test
   - Each tenant must have a folder located in `src/tests/data/` which matches the name of the tenant
   - Individual data files are placed in the `src/tests/data/<tenant-name>/` folder

3. **Query Neo4j**

   - Visit the Neo4j Browser GUI at [http://localhost:7474/](http://localhost:7474/).
   - Log in to the database using your credentials.
   - Run the following Cypher query to display all nodes:
   - `MATCH (n) RETURN n`

4. **Check metrics generated in Redis**

   - Attach shell to Redis Server container (Docker VS Code extension is recommended way)
   - Open Redis Client: `redis-cli`
   - Check all KEYS that were generated: `KEYS *`

## Running Tests

Our test suite consists of three types of tests:

- **Config Tests**: Tests our custom configuration language and verifies the expected results of each metric configuration
- **Unit Tests**: Traditional unit tests for testing individual components and functions
- **Integration Tests**: Tests integration with external services like PostgreSQL and Neo4j

### Config Tests

- Run all config tests with: `poetry run test` from the root of ict-metric-processor
- Run tests for a specific tenant: `poetry run test 'tenant_name'`
- For example: `poetry run test 'superior_uniform'`
- To understand more about our config test framework and how it uses config results, see [Config Results](#config-results-and-config-tests) below

### Unit Tests

- Run all unit tests with: `poetry run python -m unittest discover src/tests/unit_tests -v` from the ict-metric-processor root
- This will discover and run all test files in the unit_tests directory

### Integration Tests

- Run the PostgreSQL integration test with: `python3 -m unittest src/tests/integration_tests/test_postgres_integration.py -v`
- Make sure you have the API running locally and the database seeded with configs before running integration tests

## Config Results and Config Tests

### Config Results

The `config_results` list within the `MetricProcessor` stores the outcomes of individual metric processing attempts. Each element in this list is a `ConfigurationResult` object, providing detailed information about the success or failure of each metric configuration.

**Fields of a `ConfigurationResult` Object:**

- **`log_message`:** (String) A descriptive message about the processing outcome. This can include information like whether the metric was successfully processed, any errors encountered, or specific actions taken.
- **`resolved_name`:** (String) The final, fully resolved name of the metric as it's stored in Redis. This resolved name is generated based on the metric's components:
  - `node_name`: The name of the node (e.g., workstation, multishuttle)
  - `metric_type`: The type of metric (e.g., count, ratio, rate)
  - `time_window`: The time window for the metric (e.g., 60m_set)
  - `aggregation`: The aggregation method (e.g., count, sum, avg)
    For edge metrics, it represents the combined representation of the involved areas. This field is particularly important for verifying that metrics are named correctly.
- **`success`:** (Boolean) Indicates whether the metric configuration was processed successfully. `True` signifies successful processing, while `False` suggests an issue, such as a failed condition check, missing data, or an error during Redis/graph updates.
- **`config_name`:** (String) The name of the metric configuration being processed. This helps link the result back to the specific configuration section that triggered it, facilitating troubleshooting and analysis of test results.

**How `config_results` is Used:**

1. **Initialization:** The `config_results` list is initialized as an empty list at the start of each test case execution.
2. **Population during Processing:** As the `MetricProcessor` iterates through each metric configuration and processes inputs, it adds a new `ConfigurationResult` object to `config_results` for _each metric config processed_. This happens regardless of success or failure.
3. **Validation against Expected Output:** The unit tests use the `test_output` section (defined within each test case) to verify the expected values in `config_results`. The tests iterate through each expected output and compare it against the corresponding fields of the `ConfigurationResult` objects, ensuring that the `MetricProcessor` behaved as anticipated.
4. **Test Result Reporting:** The `TestJsonWrapper` uses the `config_results` list (after populating it with results by invoking `MetricProcessor.process_metrics`) to include detailed result information when generating JUnit reports. This allows for detailed examination of test results and identification of specific failures.

**Example:**

If a metric named "inventory_level" is processed successfully, a corresponding `ConfigurationResult` object might look like this:

```python
ConfigurationResult(
    log_message="Successfully processed inventory_level metric",
    resolved_name="tenant_a:facility_1:workstation:inventory_level:60m_set:count",
    success=True,
    config_name="inventory_level_config"
)
```

If the same metric fails due to a missing data field, the `ConfigurationResult` might appear as follows:

```python
ConfigurationResult(
    log_message="Failed to process inventory_level metric: Missing 'quantity' field in input data",
    resolved_name="",
    success=False,
    config_name="inventory_level_config"
)
```

By examining `config_results` within the test framework, you can gain a clear understanding of how each metric configuration performed and pinpoint specific points of failure during testing, enabling faster and more efficient debugging of the `MetricProcessor` logic and configurations.
