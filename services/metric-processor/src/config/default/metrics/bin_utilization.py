metrics = {
    ### Multishuttle Storage Location Distribution
    "multishuttle_total_locations_available": {
        "graph_operation": "area_node",
        "views": ["facility"],
        "match_conditions": {
            "aisle_code": "^.+$",
            "area_code": "^MS - Storage$",
            "empty_location_position_count": "^.+$",
        },
        "config_type": "node",
        "node_name": "multishuttle",
        "metric_type": "total_locations_available",
        "time_window": "60m_set",
        "aggregation": "sum_item_values",
        "redis_operation": "complex_event_set",
        "redis_params": {
            "identifier": "{aisle_code}",
            "value": "{empty_location_position_count}",
        },
        "description": "Number of available locations within the DMS Picking Buffer",
    },
    "multishuttle_total_locations_occupied": {
        "graph_operation": "area_node",
        "views": ["facility"],
        "match_conditions": {
            "aisle_code": "^.+$",
            "area_code": "^MS - Storage$",
            "empty_location_position_count": "^.+$",
            "total_location_position_count": "^.+$",
        },
        "config_type": "node",
        "node_name": "multishuttle",
        "metric_type": "total_locations_occupied",
        "time_window": "60m_set",
        "aggregation": "sum_item_values",
        "redis_operation": "total_storage_locations_occupied",
        "description": "Number of occupied locations within the DMS Picking Buffer",
    },
    #########################################################
    # Miniload - Distinct number of aisles
    "miniload_aisle_available_count": {
        "views": ["facility"],
        "graph_operation": "area_node",
        "match_conditions": {
            "area_code": "^ML - Storage$",
            "empty_location_position_count": "^.+$",  # greater than 0
            "aisle_code": "^.+$",
        },
        "config_type": "node",
        "node_name": "miniload",
        "metric_type": "aisles_available",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_count",
        "redis_params": {"identifier": "{aisle_code}"},
        "description": "Number of available aisles within the Mini-Load Buffer",
    },
    # Miniload - number of available locations (across all aisles)
    "miniload_total_locations_available": {
        "graph_operation": "area_node",
        "views": ["facility"],
        "match_conditions": {
            "area_code": "^ML - Storage$",
            "empty_location_position_count": "^.+$",
            "aisle_code": "^.+$",
        },
        "config_type": "node",
        "node_name": "miniload",
        "metric_type": "total_locations_available",
        "time_window": "60m_set",
        "aggregation": "sum_item_values",
        "redis_operation": "complex_event_set",
        "redis_params": {
            "value": "{empty_location_position_count}",
            "identifier": "{aisle_code}",
        },
        "description": "Number of available locations per aisle within the Mini-Load Buffer",
    },
    # Miniload - number of occupied locations (across all aisles)
    "miniload_total_locations_occupied": {
        "graph_operation": "area_node",
        "views": ["facility"],
        "match_conditions": {
            "area_code": "^ML - Storage$",
            "empty_location_position_count": "^.+$",
            "total_location_position_count": "^.+$",
            "aisle_code": "^.+$",
        },
        "config_type": "node",
        "node_name": "miniload",
        "metric_type": "total_locations_occupied",
        "time_window": "60m_set",
        "aggregation": "sum_item_values",
        "redis_operation": "total_storage_locations_occupied",
        "description": "Number of occupied locations within the Mini-Load Buffer",
    },
    #################################################
    ### Multishuttle Aisle Module Metrics
    # Aisle - Storage Utilization
    "multishuttle_aisle_storage_utilization": {
        "views": ["multishuttle", "MSAI{aisle_code}"],
        "graph_operation": "area_node",
        "parent_nodes": ["multishuttle"],
        "match_conditions": {
            "area_code": "^MS - Storage$",
            "empty_location_position_count": "^.+$",
            "total_location_position_count": "^.+$",
            "aisle_code": "^.+$",
        },
        "config_type": "node",
        "label": "Aisle",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "storage_utilization",
        "time_window": "value",
        "aggregation": "static",
        "redis_operation": "storage_utilization",
        "metric_units": "%",
        "description": "Ratio of occupied locations to total locations within this DMS aisle.",
    },
    # Aisle - Storage Location Distribution - Available
    "multishuttle_aisle_storage_location_distribution_available": {
        "graph_operation": "area_node",
        "views": ["multishuttle", "MSAI{aisle_code}"],
        "parent_nodes": ["multishuttle"],
        "match_conditions": {
            "area_code": "^MS - Storage$",
            "empty_location_position_count": "^.+$",
            "aisle_code": "^.+$",
        },
        "config_type": "node",
        "label": "Aisle",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "location_distribution_available",
        "time_window": "value",
        "aggregation": "static",
        "redis_operation": "storage_location_distribution_available",
        "description": "Number of available locations within this DMS aisle.",
    },
    # Aisle - Storage Location Distribution - Occupied
    "multishuttle_aisle_storage_location_distribution_occupied": {
        "graph_operation": "area_node",
        "views": ["multishuttle", "MSAI{aisle_code}"],
        "parent_nodes": ["multishuttle"],
        "match_conditions": {
            "area_code": "^MS - Storage$",
            "empty_location_position_count": "^.+$",
            "total_location_position_count": "^.+$",
            "aisle_code": "^.+$",
        },
        "config_type": "node",
        "label": "Aisle",
        "node_name": "MSAI{aisle_code}",
        "metric_type": "location_distribution_occupied",
        "time_window": "value",
        "aggregation": "static",
        "redis_operation": "storage_location_distribution_occupied",
        "description": "Number of occupied locations within this DMS aisle.",
    },
    # Aisle - Locations Available
    "multishuttle_aisle_locations_available_count": {
        "aggregation": "static",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "Aisle",
        "match_conditions": {
            "aisle_code": "^.+$",
            "area_code": "^MS - Storage$",
            "empty_location_position_count": "^.+$",
        },
        "metric_type": "locations_available",
        "node_name": "MSAI{aisle_code}",
        "parent_nodes": ["multishuttle"],
        "redis_operation": "storage_location_distribution_available",
        "time_window": "value",
        "views": ["multishuttle"],
        "description": "Number of available locations within this DMS aisle.",
    },
    # Aisle - Locations Occupied
    "multishuttle_aisle_locations_occupied": {
        "aggregation": "static",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "Aisle",
        "match_conditions": {
            "area_code": "^MS - Storage$",
            "empty_location_position_count": "^.+$",
            "total_location_position_count": "^.+$",
            "aisle_code": "^.+$",
        },
        "metric_type": "locations_occupied",
        "node_name": "MSAI{aisle_code}",
        "parent_nodes": ["multishuttle"],
        "redis_operation": "storage_location_distribution_occupied",
        "time_window": "value",
        "views": ["multishuttle"],
        "description": "Number of occupied locations within this DMS aisle.",
    },
    #################################################
    # Aisle Metrics within miniload graph-view
    # Miniload Locations Per Aisle Available
    "aisle_locations_available_count": {
        "graph_operation": "area_node",
        "views": ["miniload"],
        "parent_nodes": ["miniload"],
        "match_conditions": {
            "aisle_code": "^.+$",
            "area_code": "^ML - Storage$",
            "empty_location_position_count": "^.+$",
        },
        "config_type": "node",
        "label": "Aisle",
        "node_name": "MLAI{aisle_code}",
        "metric_type": "locations_available",
        "time_window": "value",
        "aggregation": "static",
        "redis_operation": "storage_location_distribution_available",
        "description": "Number of available locations within this Mini-Load aisle",
    },
    # # Miniload Locations Per Aisle Occupied
    "aisle_locations_occupied": {
        "graph_operation": "area_node",
        "views": ["miniload"],
        "parent_nodes": ["miniload"],
        "match_conditions": {
            "area_code": "^ML - Storage$",
            "empty_location_position_count": "^.+$",
            "total_location_position_count": "^.+$",
            "aisle_code": "^.+$",
        },
        "config_type": "node",
        "label": "Aisle",
        "node_name": "MLAI{aisle_code}",
        "metric_type": "locations_occupied",
        "time_window": "value",
        "aggregation": "static",
        "redis_operation": "storage_location_distribution_occupied",
        "description": "Number of occupied locations within this Mini-Load aisle.",
    },
}
