metrics = {
    # Workstations - Cycle Count
    "workstations_cycle_count": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workflow_code": "CycleCount",
        },
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "cycle_count",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total cycle counts across all stations in the last hour.",
    },
    # Workstations - Cycle Count Rate
    "workstations_cycle_count_rate": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workflow_code": "CycleCount",
        },
        "metric_units": "/hr",
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "cycle_count_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "description": "Hourly rate of cycle counts across all stations based on the last 15 minutes.",
    },
    ## Workstations - Orders Picked Rate ##
    "workstations_orders_picked_rate": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workflow_code": "GTPPrint",
        },
        "metric_units": "/hr",
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "orders_picked_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "description": "Orders picked hourly rate based on the last 15 minutes.",
    },
    ## Workstations - Orders Picked Count ##
    "workstations_orders_picked_count": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workflow_code": "GTPPrint",
        },
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "orders_picked_count",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total orders picked in the last hour.",
    },
    ## Workstations - Order Lines Picked Rate ##
    "workstations_order_lines_picked_rate": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workflow_code": "PICK",
        },
        "metric_units": "/hr",
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "order_lines_picked_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "description": "Hourly rate of order lines picked across all stations based on the last 15 minutes.",
    },
    ## Workstations - Order Lines Picked Count ##
    "workstations_order_lines_picked_count": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workflow_code": "PICK",
        },
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "order_lines_picked_count",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "description": "Total order lines picked across all stations in the last hour.",
    },
    # Workstations - Order Quantity Picked #
    "workstations_order_quantity_picked": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workflow_code": "PICK",
        },
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "order_quantity_picked",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "event_set",
        "redis_params": {
            "value": "{picked_qty}",
        },
        "description": "Total order quantity picked across all stations in the last hour.",
    },
    # Workstations - Order Quantity Picked by Destination #
    # Holding off on this for now, belongs in a drill down view of Workstations,
    # not in Facility View.
    # "workstations_order_quantity_picked_by_destination": {
    #     "config_type": "node",
    #     "match_conditions": {
    #         "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
    #         "workflow_code": "PICK",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:workstations:order_quantity_picked_{induction_zone_code}:60m_set:sum",
    #     "redis_params": {
    #         "value": "{picked_qty}",
    #     },
    #     "redis_operation": "event_set",
    # },
    # Workstations - Order Quantity Throughput/Rate #
    # "workstations_order_quantity_picked_throughput": {
    #     "config_type": "node",
    #     "match_conditions": {
    #         "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
    #         "workflow_code": "PICK",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:workstations:order_quantity_throughput:15m_set:sum",
    #     "redis_params": {
    #         "ttl": 900,
    #         "value": "{picked_qty}",
    #     },
    #     "redis_operation": "event_set",
    # },
    ##############################################
    # Work Station Metrics
    ##############################################
    ## Workstation - Order Lines Picked Rate ##
    "workstation_order_lines_picked_rate": {
        "views": ["workstations"],
        "config_type": "node",
        "label": "Station",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workflow_code": "PICK",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "/hr",
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "order_lines_picked_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "parent_nodes": ["workstations"],
        "description": "Hourly rate of order lines picked in this station based on the last 15 minutes.",
    },
    "workstation_order_lines_picked_count": {
        "views": ["workstations"],
        "config_type": "node",
        "label": "Station",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workflow_code": "PICK",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "order_lines_picked",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "parent_nodes": ["workstations"],
        "description": "Total order lines picked in this station in the last hour.",
    },
    "workstation_cycle_counts": {
        "views": ["workstations"],
        "config_type": "node",
        "label": "Station",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workflow_code": "CycleCount",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "cycle_counts",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "parent_nodes": ["workstations"],
        "description": "Total cycle counts in this station in the last hour.",
    },
    # Workstation - Cycle Count Rate
    "workstation_cycle_count_rate": {
        "views": ["workstations"],
        "config_type": "node",
        "label": "Station",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workflow_code": "CycleCount",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "/hr",
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "cycle_count_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "description": "Hourly rate of cycle counts in this station based on the last 15 minutes.",
    },
    "workstation_order_lines_picked_by_destination": {
        "views": ["workstations"],
        "config_type": "node",
        "label": "Station",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workflow_code": "PICK",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "order_lines_picked_{induction_zone_code}",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "parent_nodes": ["workstations"],
        "description": "Total order lines picked across all destination locations in this station in the last hour.",
    },
}
