metrics = {
    "receiving_outbound_units_edge": {
        "views": ["facility", "multishuttle"],
        "config_type": "outbound-edge",
        "match_conditions": {
            "source_location_code": "RECV-INDUCT",
            "handling_unit_code": "^.+$",
        },
        "hu_id": "handling_unit_code",
        "outbound_area": "receiving",
        "units": "handling_unit",
        "description": "Hourly rate of outbound units departing the receiving area based on last 15 minutes.",
    },
    ##############################################################
    # These 2 configs were commented out before the refactor.
    ## Multishuttle Node Metrics ##
    # Multishuttle Inbound Node Config
    # "multishuttle_inbound_totes_count": {
    #     "config_type": "node",
    #     "match_conditions": {
    #         "destination_location_code": "MSAI.*PS",
    #         "handling_unit_code": "^.+$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:inbound_totes_count:60m_set:count",
    #     "redis_operation": "event_set",
    # },
    # # Multishuttle Outbound Node Config
    # "multishuttle_outbound_totes_count": {
    #     "config_type": "node",
    #     "match_conditions": {
    #         "source_location_code": "MSAI.*DS",
    #         "handling_unit_code": "^.+$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:outbound_totes_count:60m_set:count",
    #     "redis_operation": "event_set",
    # },
    ##############################################################
    ## Multishuttle Edge Metric Configs ##
    # Multishuttle Inbound Edge Config
    "multishuttle_inbound_totes_edge": {
        "graph_operation": "area_edge",
        "views": ["facility", "workstations"],
        "hu_id": "handling_unit_code",
        "inbound_area": "multishuttle",
        "config_type": "inbound-edge",
        "match_conditions": {
            "destination_location_code": "MSAI.*PS",
            "handling_unit_code": "^.+$",
        },
        "redis_operation": "event_set",
        "description": "Hourly rate of Inbound units arriving at the DMS picking buffer based on last 15 minutes.",
    },
    # Multishuttle Outbound Edge Config
    "multishuttle_outbound_totes_edge": {
        "views": ["facility", "workstations"],
        "config_type": "outbound-edge",
        "match_conditions": {
            "source_location_code": "MSAI.*DS",
            "handling_unit_code": "^.+$",
        },
        "outbound_area": "multishuttle",
        "units": "handling_unit",
        "hu_id": "handling_unit_code",
        "description": "Hourly rate of outbound units departing the DMS picking buffer based on last 15 minutes.",
    },
    # Multishuttle Aisle Inbound Edge Config
    "multishuttle_aisle_inbound_totes_edge": {
        "graph_operation": "area_edge",
        "views": ["multishuttle"],
        "hu_id": "handling_unit_code",
        "inbound_area": "{destination_location_code}",
        "config_type": "inbound-edge",
        "label": "Aisle",
        "area_name": "multishuttle",
        "inbound_parent_nodes": ["multishuttle"],
        "match_conditions": {
            "destination_location_code": "MSAI.*PS",
            "handling_unit_code": "^.+$",
        },
        "name_formula": {
            "source": "{destination_location_code}",
            "pattern": r"MSAI(?P<aisle>\d{2}).*",
            "template": "MSAI{aisle}",
        },
        "redis_operation": "event_set",
        "description": "Hourly rate of inbound units arriving at the DMS picking buffer per aisle based on last 15 minutes.",
    },
    # Multishuttle Aisle Outbound Edge Config
    "multishuttle_aisle_outbound_totes_edge": {
        "views": ["multishuttle"],
        "config_type": "outbound-edge",
        "label": "Aisle",
        "area_name": "multishuttle",
        "match_conditions": {
            "source_location_code": "MSAI.*DS",
            "handling_unit_code": "^.+$",
        },
        "outbound_area": "{source_location_code}",
        "outbound_parent_nodes": ["multishuttle"],
        "name_formula": {
            "source": "{source_location_code}",
            "pattern": r"MSAI(?P<aisle>\d{2}).*",
            "template": "MSAI{aisle}",
        },
        "units": "handling_unit",
        "hu_id": "handling_unit_code",
        "description": "Hourly rate of outbound units departing the DMS picking buffer per aisle based on last 15 minutes.",
    },
    ######################################################################
    # Workstations Inbound from Multishuttle Outbound Edge Config
    "workstations_inbound_totes_edge": {
        "graph_operation": "area_edge",
        "views": ["facility", "multishuttle"],
        "config_type": "inbound-edge",
        "match_conditions": {
            "destination_location_code": "M.*GTP.*D1",
            "handling_unit_code": "^.+$",
        },
        "inbound_area": "workstations",
        "hu_id": "handling_unit_code",
        "redis_operation": "event_set",
        "description": "Hourly rate of inbound units arriving at the Workstations area based on last 15 minutes.",
    },
    # Workstations Outbound to Multishuttle Inbound Edge Config
    "workstations_outbound_totes_edge": {
        "views": ["facility", "multishuttle"],
        "config_type": "outbound-edge",
        "match_conditions": {
            "source_location_code": "M.*GTP.*D1",
            "handling_unit_code": "^.+$",
        },
        "outbound_area": "workstations",
        "units": "handling_unit",
        "hu_id": "handling_unit_code",
        "description": "Hourly rate of outbound units departing the Workstations area based on last 15 minutes.",
    },
    # Workstations Workstation Inbound Edge Config
    "workstations_workstation_inbound_totes_edge": {
        "graph_operation": "area_edge",
        "views": ["workstations"],
        "hu_id": "handling_unit_code",
        "label": "Station",
        "inbound_area": "{destination_location_code}",
        "config_type": "inbound-edge",
        "inbound_parent_nodes": ["workstations"],
        "match_conditions": {
            "destination_location_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "handling_unit_code": "^.+$",
        },
        "name_formula": {
            "source": "{destination_location_code}",
            "pattern": "(?P<workstation_code>.*)(.{2}$)",  # remove the last two characters from the location code to get the workstation code
            "template": "{workstation_code}",
        },
        "redis_operation": "event_set",
        "description": "Hourly rate of inbound units arriving at each work station based on last 15 minutes.",
    },
    # Workstations Workstation Outbound Edge Config
    "workstations_workstation_outbound_totes_edge": {
        "views": ["workstations"],
        "config_type": "outbound-edge",
        "match_conditions": {
            "source_location_code": "^M.*GTP.*D1$",
            "handling_unit_code": "^.+$",
        },
        "outbound_area": "{source_location_code}",
        "name_formula": {
            "source": "{source_location_code}",
            "pattern": "(?P<workstation_code>.*)(.{2}$)",  # remove the last two characters from the location code to get the workstation code
            "template": "{workstation_code}",
        },
        "units": "handling_unit",
        "label": "Station",
        "outbound_parent_nodes": ["workstations"],
        "hu_id": "handling_unit_code",
        "description": "Hourly rate of outbound units departing each work station based on last 15 minutes.",
    },
    ##############################################################
    ## Receiving Area Metrics ##
    # Receiving Stock Time Start #
    "receiving_stock_time_start": {
        "views": ["facility", "multishuttle"],
        "config_type": "node",
        "match_conditions": {
            "destination_location_code": "RECV-INDUCT",
            "handling_unit_code": "^.+$",
        },
        "metric_units": "mins",
        "graph_operation": "area_node",
        "node_name": "receiving",
        "metric_type": "stock_time",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "cycle_time_start",
        "redis_params": {"instance_id": "{handling_unit_code}"},
        "units": "case",
        "description": "Total time between a unit being inducted into a stock location and being inducted to the DMS picking buffer.",
    },
    # Receiving Stock Time End #
    "receiving_stock_time_stop": {
        "views": ["facility", "multishuttle"],
        "config_type": "node",
        "match_conditions": {
            "source_location_code": "RECV-INDUCT",
            "handling_unit_code": "^.+$",
        },
        "graph_operation": "area_node",
        "metric_units": "mins",
        "node_name": "receiving",
        "metric_type": "stock_time",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "cycle_time_stop",
        "redis_params": {
            "instance_id": "{handling_unit_code}",
        },
        "units": "case",
        "description": "Total time between a unit being inducted into a stock location and being inducted to the DMS picking buffer.",
    },
    ##############################################################
    # These 2 miniload configs were commented out before the refactor.
    ## Mini load Node Metrics ##
    # Miniload Inbound Totes Count Config
    # "miniload_inbound_totes_count": {
    #     "config_type": "node",
    #     "match_conditions": {
    #         "destination_location_code": "ML.*I.*",
    #         "handling_unit_code": "^.+$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:miniload:inbound_totes_count:60m_set:count",
    #     "redis_operation": "event_set",
    # },
    # # Miniload Outbound Totes Count Config
    # "miniload_outbound_totes_count": {
    #     "config_type": "node",
    #     "match_conditions": {
    #         "source_location_code": "ML.*O.*",
    #         "handling_unit_code": "^.+$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:miniload:outbound_totes_count:60m_set:count",
    #     "redis_operation": "event_set",
    # },
    ##############################################################
    # Miniload Inbound Edge Config
    "miniload_inbound_totes_edge": {
        "graph_operation": "area_edge",
        "views": [
            "facility",
        ],
        "config_type": "inbound-edge",
        "match_conditions": {
            "destination_location_code": "ML.*I.*",
            "handling_unit_code": "^.+$",
        },
        "inbound_area": "miniload",
        "hu_id": "handling_unit_code",
        "redis_operation": "event_set",
        "description": "Hourly rate of inbound units arriving at the Miniload area based on last 15 minutes.",
    },
    # Miniload Outbound Edge Config
    "miniload_outbound_totes_edge": {
        "views": [
            "facility",
        ],
        "config_type": "outbound-edge",
        "match_conditions": {
            "source_location_code": "ML.*O.*",
            "handling_unit_code": "^.+$",
        },
        "outbound_area": "miniload",
        "units": "handling_unit",
        "hu_id": "handling_unit_code",
        "description": "Hourly rate of outbound units departing the Miniload area based on last 15 minutes.",
    },
    # Packing Inbound Edge Metric
    # "packing_inbound_totes_edge": {
    #     "graph_operation": "area_edge",
    #     "views": ["facility", "multishuttle"],
    #     "config_type": "inbound-edge",
    #     "match_conditions": {
    #         "destination_location_code": "^PACK-STN-.*",
    #         "handling_unit_code": "^.+$",
    #     },
    #     "inbound_area": "packing",
    #     "units": "handling_unit",
    #     "hu_id": "handling_unit_code",
    #     "redis_operation": "event_set",
    # },
    # # Packing Outbound Edge Metric
    # "packing_outbound_totes_edge": {
    #     "views": ["facility", "multishuttle"],
    #     "config_type": "outbound-edge",
    #     "match_conditions": {
    #         "source_location_code": "^PACK-STN-.*",
    #         "handling_unit_code": "^.+$",
    #     },
    #     "outbound_area": "packing",
    #     "units": "handling_unit",
    #     "hu_id": "handling_unit_code",
    # },
}
