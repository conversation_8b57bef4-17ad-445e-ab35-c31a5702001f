# Setup

## Overview

This repository includes a suite of end-to-end UI tests that have been written for 'Intelligent Control Tower' app using the Playwright test framework for Node.js.
This framework is designed to automate web application testing with ease. It includes an easy-to-use API, support for parallel test execution, and a reporting system.
To learn more about Playwright and how it can be used for your own testing needs, visit the official [Playwright documentation](https://playwright.dev/).

## Prerequisites

To utilize the Playwright framework, you will need to have [Node.js](https://nodejs.org/en) installed, preferably version 14 or higher and [Git](https://git-scm.com/).
You can confirm the version of Node.js installed on your system by running the command

```bash
  node -v
```

Homebrew is also required in order to install some dependencies.

```bash
  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

Follow the steps below to get started with the Test Automation Framework:

### Install

```bash
  yarn install
```

### Configuration

In the root of this project, add a `.env` file.  
 Refer to the `example.env`. for an example.

## UI Test Environments

```bash
- local-dev     :(local UI / dev api)
- dev           :(dev UI / dev api)
- stage         :(stage UI / stage api)
- prod          :(prod UI / prod api)
```

> Note: Setting the environment is set in the .env file with the ICT_ENV variable.

## Project structure

```bash
├── ./src
│   ├── /app
│   ├── /components
│   ├── /core
│   ├── /services
│   ├── /types
│   ├── /views
│   │   ├── /dashboard
│   │   ├── /login
├── ./test
│    ├── /fixtures
│    ├── /smoke
│    ├── /views
│        ├── /dashboards
└──
```

## Running Tests

### Local

- Run smoke tests

```bash
yarn test:smoke
```

- Run Regression tests

```bash
yarn test:regression
```

### CI/CD

- Run smoke tests

```bash
yarn test:smoke:ci
```

- Run Regression tests

```bash
yarn test:regression:ci
```
