{"name": "control-tower", "version": "1.0.0", "description": "Control Tower Monorepo", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "prepare": "husky install", "lint-staged": "npx lint-staged", "pre-commit": "### This is a temporary workaround until we get a monorepo tool in place ### \n cd services/api && yarn run pre-commit"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"ajv": "^8.17.1", "husky": "^8.0.1", "json-schema-to-ts": "^3.1.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "devDependencies": {"@types/ajv": "1.0.4"}}