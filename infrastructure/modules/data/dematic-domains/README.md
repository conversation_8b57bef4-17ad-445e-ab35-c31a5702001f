<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# Dematic Domains Data Module

## Table of Contents

- [Description](#description)
- [Usage](#usage)
- [Maintenance](#maintenance)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module outputs `google_managed_dns_zone` [data sources](https://developer.hashicorp.com/terraform/language/data-sources) for each of the four root GCP managed DNS zones (not owned by Control Tower) that we use for Control Tower subdomains. It doesn't create any resources, and the project ID and managed zone names are hard-coded into the locals file so doesn't take any variables. This allows other modules to instantiate it with no input and gain access to the current resource property values in GCP.

> The project ID and zone names have to be maintained somewhere - by doing it in a module, we eliminate having to pass around that project ID and zone name as variables to our other modules.

 See the Terraform [google\_dns\_managed\_zone](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/dns_managed_zone) documentation for details about the resource, including the exposed properties.

## Usage

To use the module, instantiate it in your module and reference the zone(s) that you need like you would any other module attribute.

For example:

``` yaml
module "dematic_domains" {
  source = "path_to_module"
}

locals {
  subdomain_name = "${var.subdomain_prefix}.${module.dematic_domains.dev_dns_zone.name}"
  subdomain_dns_name = "${var.subdomain_prefix}.${module.dematic_domains.dev_dns_zone.dns_name}"
  resource_name = replace(local.subdomain_name, ".", "-") # e.g., "ict-dematic-cloud" for "ict.dematic.cloud"

  root_domain_project = module.dematic_domains.dev_dns_zone.project
}

resource "google_managed_dns_zone" "main" {
  project = var.project_id

  name = local.resource_name
  dns_name = local.subdomain_dns_name
}

resource "google_dns_record_set" "parent_ns" {
  project = local.root_domain_project

  name = local.subdomain_dns_name
  type = "NS"

  managed_zone = module.dematic_domains.dev_dns_zone.name
  rrdatas      = google_dns_managed_zone.main.name_servers
  ttl          = coalesce(var.parent_ns_ttl, 3600)
}

```

## Maintenance

In the unlikely event that any of the four DNS managed zones we use are recreated in another project, or renamed in the current project, `locals.tf` will need to be updated accordingly.

The current module design matches the nonprod and prod landing zone architecture. If that changes, the data sources and outputs may need to be renamed to make more sense with the new paradigm.

## Usage

Basic usage of this module is as follows:

```hcl
module "dematic-domains" {
  source  = "gitlab.com/dematic/control-tower-dematic-domains/google"
  version = "~> 0.1.0"
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_dns_managed_zone.dev](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/dns_managed_zone) | data source |
| [google_dns_managed_zone.dev_alt](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/dns_managed_zone) | data source |
| [google_dns_managed_zone.prod](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/dns_managed_zone) | data source |
| [google_dns_managed_zone.prod_alt](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/dns_managed_zone) | data source |

## Inputs

No inputs.

## Outputs

| Name | Description |
|------|-------------|
| nonprod | The DNS zone for nonprod environments. |
| nonprod\_alt | The alternative DNS zone for the development environment. |
| prod | The DNS zone for the production environment. |
| prod\_alt | The alternative DNS zone for the production environment. |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
}
```

<!-- END_TFVARS_DOCS-->
