<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# Common Services Terraform Module

WIP

## Table of Contents

- [Description](#description)
- [DNS](#dns)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates and manages the services that are used by all Control Tower environments. This includes:

- Docker Image Attestation
- DNS
- Artifact Registry Repositories

## DNS

The `dns.tf` file defines the non-prod and prod DNS managed zones. These will serve as the parent DNS zones to all environments deployed in the non-prod and prod landing zones, respectively. It also creates NS records in the corresponding root Dematic non-prod and prod managed zones to delegate the subdomains to the Control Tower managed zones.

For example: if the DNS name of the non-prod root zone is `dematic.dev.`, then the Control Tower non-prod subdomain's DNS name would be `ict.dematic.dev.`.

Information about the root Dematic non-prod zone is retrieved from the `dematic-domains` Terraform module, which outputs a `data "google_managed_dns_zone"` resource for each of the zones used by Control Tower. For more information about this module, see the README in `infrastructure/modules/data/dematic-domains`.

## Usage

Basic usage of this module is as follows:

```hcl
module "common-services" {
  source  = "gitlab.com/dematic/control-tower-common-services/google"
  version = "~> 0.1.0"

  # Required variables
  artifact_registry_configuration = var.artifact_registry_configuration
  common_resource_labels          = var.common_resource_labels
  domain_config                   = var.domain_config
  gitlab_runner_permissions       = var.gitlab_runner_permissions
  naming_root                     = var.naming_root
  project_id                      = var.project_id
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| dematic\_domains | ../../data/dematic-domains | n/a |

## Resources

| Name | Type |
|------|------|
| [google_artifact_registry_repository.docker](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/artifact_registry_repository) | resource |
| [google_artifact_registry_repository.generic](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/artifact_registry_repository) | resource |
| [google_artifact_registry_repository.npm](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/artifact_registry_repository) | resource |
| [google_artifact_registry_repository.python](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/artifact_registry_repository) | resource |
| [google_binary_authorization_attestor.attestor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/binary_authorization_attestor) | resource |
| [google_container_analysis_note.attestor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/container_analysis_note) | resource |
| [google_dns_managed_zone.nonprod](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/dns_managed_zone) | resource |
| [google_dns_managed_zone.prod](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/dns_managed_zone) | resource |
| [google_dns_record_set.nonprod_ns](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/dns_record_set) | resource |
| [google_dns_record_set.prod_ns](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/dns_record_set) | resource |
| [google_kms_crypto_key.attestor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/kms_crypto_key) | resource |
| [google_kms_key_ring.attestor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/kms_key_ring) | resource |
| [google_project_iam_member.gitlab_runner_permissions](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member) | resource |
| [google_kms_crypto_key_version.attestor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/kms_crypto_key_version) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| artifact\_registry\_configuration | The configuration info for setting up the Artifact Registry repositories. Docker and the      generic site assets repositories are always enabled.      Variables:     - location: The location to deploy the Artifact Registry repositories. Examples: 'us',          'us-central1'.     - enable\_npm: Whether or not to enable the npm registry.     - enable\_python: Whether or not to enable the Python registry. | ```object({ location = string enable_npm = bool enable_python = bool })``` | n/a | yes |
| common\_resource\_labels | The common labels that should be applied to all resources that can be labeled. | `map(string)` | n/a | yes |
| domain\_config | n/a | ```object({ nonprod = object({ subdomain_prefix = string zone_visibility = string }) ns_record_ttl = number })``` | n/a | yes |
| gitlab\_runner\_permissions | The GitLab Runner service account, and the roles that should be granted to it. | ```object({ service_account = string roles = list(string) })``` | n/a | yes |
| naming\_root | The root identifier for all Control Tower resources. This value will be combined with the     `environment` to create a unique prefix for any resources requiring a globally-unique name.      This value is intended to be shared across all Control Tower deployments. As of module      creation, this value is "ict". | `string` | n/a | yes |
| project\_id | The ID of the shared services GCP project to deploy to. | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| attestor | The attestor configuration.      Outputs:     - attestor\_name: The name of the attestor.     - kms\_key\_ring\_id: The ID of the KMS key ring used by the attestor. |
| docker\_repository | The Docker repository configuration.      Outputs:     - project: The ID of the project where the Docker repository is located.     - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.     - repository: The name of the Docker repository. |
| generic\_repository | The generic repository configuration.      Outputs:     - project: The ID of the project where the generic repository is located.     - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.     - repository: The name of the generic repository. |
| nonprod\_domain | The domain for the nonprod landing zone. This will serve as the root domain for all nonprod      environments. |
| npm\_repository | The npm repository configuration. Null of the npm repository is not enabled.      Outputs:     - project: The ID of the project where the npm repository is located.     - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.     - repository: The name of the npm repository, if enabled. |
| prod\_domain | The domain for the prod landing zone. This will serve as the root domain for all prod     environments. |
| python\_repository | The Python repository configuration. Null of the Python repository is not enabled.      Outputs:     - project: The ID of the project where the Python repository is located.     - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.     - repository: The name of the Python repository, if enabled. |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
  #     The configuration info for setting up the Artifact Registry repositories. Docker and the
  #     generic site assets repositories are always enabled.
  #
  #     Variables:
  #     - location: The location to deploy the Artifact Registry repositories. Examples: 'us',
  #         'us-central1'.
  #     - enable_npm: Whether or not to enable the npm registry.
  #     - enable_python: Whether or not to enable the Python registry.
  #
  artifact_registry_configuration = ""
  
  # The common labels that should be applied to all resources that can be labeled.
  common_resource_labels = ""
  
  domain_config = ""
  
  # The GitLab Runner service account, and the roles that should be granted to it.
  gitlab_runner_permissions = ""
  
  #     The root identifier for all Control Tower resources. This value will be combined with the
  #     `environment` to create a unique prefix for any resources requiring a globally-unique name.
  #     This value is intended to be shared across all Control Tower deployments. As of module
  #     creation, this value is "ict".
  #
  naming_root = ""
  
  # The ID of the shared services GCP project to deploy to.
  project_id = ""
}
```

<!-- END_TFVARS_DOCS-->
