<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# Common Services Terraform Module

## Table of Contents

- [Description](#description)
- [Components](#components)
- [DNS Management](#dns-management)
- [Artifact Registry](#artifact-registry)
- [GitLab Integration](#gitlab-integration)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates and manages the foundational services that are shared across all Control Tower environments. It establishes the core infrastructure components required for secure, scalable deployment of Control Tower applications.

The module provisions:

- **DNS Management**: Managed zones for prod and non-prod environments
- **Artifact Registry Repositories**: Secure storage for Docker images, npm packages, Python packages, and generic artifacts
- **GitLab Integration**: Service account permissions for CI/CD pipelines

## Components

### DNS Management

The module creates managed DNS zones that serve as the parent zones for all Control Tower environments:

- **Non-Production Zone**: Hosts development and staging environments
- **Production Zone**: Hosts production environments  
- **Delegation Records**: NS records in parent Dematic zones to delegate subdomains

**DNS Hierarchy Example**:
```
dematic.dev (parent zone)
└── ict.dematic.dev (Control Tower non-prod zone)
    ├── dev-us1.ict.dematic.dev
    └── stage-us1.ict.dematic.dev

dematic.com (parent zone)
└── ict.dematic.com (Control Tower prod zone)
    └── prod-us1.ict.dematic.com
```

### Artifact Registry

Multiple repositories are created to support different artifact types:

- **Docker Repository**: Container images for all services
- **Generic Repository**: Static assets and miscellaneous artifacts
- **npm Repository**: Node.js packages (optional)
- **Python Repository**: Python packages for metric processor (optional)

All repositories include:
- Vulnerability scanning
- Access controls
- Regional replication
- Lifecycle management policies

### GitLab Integration

Configures service account permissions for CI/CD operations:

- **Build Permissions**: Access to Artifact Registry for pushing images
- **Deployment Permissions**: Access to deploy applications

## DNS Management

The DNS configuration creates a hierarchical structure that supports:

1. **Environment Isolation**: Separate subdomains for each environment
2. **Regional Deployment**: Support for multi-region deployments  
3. **Service Discovery**: Consistent naming for internal services
4. **Certificate Management**: Automated SSL certificate provisioning

**Zone Structure**:
- **Root Integration**: Connects to existing Dematic DNS infrastructure
- **Subdomain Delegation**: Allows Control Tower to manage its own DNS
- **Record Management**: Supports dynamic record creation for services

## Artifact Registry

The Artifact Registry setup provides:

1. **Multi-Format Support**: Different repositories for different artifact types
2. **Security Scanning**: Automatic vulnerability detection
3. **Access Control**: IAM-based permissions for repositories
4. **Regional Replication**: Multi-region availability for performance

**Repository Types**:
- **Docker**: All application container images
- **Generic**: Static assets, configuration files, documentation
- **npm**: Node.js packages and libraries (optional)
- **Python**: Python packages for data processing (optional)

## GitLab Integration

The GitLab service account configuration provides:

1. **Secure Access**: Minimal necessary permissions for CI/CD
2. **Role Separation**: Different permissions for different pipeline stages
3. **Audit Trail**: Complete record of CI/CD operations
4. **Scalability**: Support for multiple concurrent pipelines

**Permission Scopes**:
- **Artifact Registry**: Push/pull access for build artifacts
- **Cloud Run**: Deploy applications to Cloud Run

## Usage

Basic usage of this module is as follows:

```hcl
module "common-services" {
  source  = "gitlab.com/dematic/control-tower-common-services/google"
  version = "~> 0.1.0"

  # Required variables
  artifact_registry_configuration = var.artifact_registry_configuration
  common_resource_labels          = var.common_resource_labels
  domain_config                   = var.domain_config
  gitlab_runner_permissions       = var.gitlab_runner_permissions
  naming_root                     = var.naming_root
  project_id                      = var.project_id
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| dematic\_domains | ../../data/dematic-domains | n/a |

## Resources

| Name | Type |
|------|------|
| [google_artifact_registry_repository.docker](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/artifact_registry_repository) | resource |
| [google_artifact_registry_repository.generic](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/artifact_registry_repository) | resource |
| [google_artifact_registry_repository.npm](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/artifact_registry_repository) | resource |
| [google_artifact_registry_repository.python](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/artifact_registry_repository) | resource |
| [google_binary_authorization_attestor.attestor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/binary_authorization_attestor) | resource |
| [google_container_analysis_note.attestor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/container_analysis_note) | resource |
| [google_dns_managed_zone.nonprod](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/dns_managed_zone) | resource |
| [google_dns_managed_zone.prod](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/dns_managed_zone) | resource |
| [google_dns_record_set.nonprod_ns](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/dns_record_set) | resource |
| [google_dns_record_set.prod_ns](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/dns_record_set) | resource |
| [google_kms_crypto_key.attestor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/kms_crypto_key) | resource |
| [google_kms_key_ring.attestor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/kms_key_ring) | resource |
| [google_project_iam_member.gitlab_runner_permissions](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member) | resource |
| [google_kms_crypto_key_version.attestor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/kms_crypto_key_version) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| artifact\_registry\_configuration | The configuration info for setting up the Artifact Registry repositories. Docker and the      generic site assets repositories are always enabled.      Variables:     - location: The location to deploy the Artifact Registry repositories. Examples: 'us',          'us-central1'.     - enable\_npm: Whether or not to enable the npm registry.     - enable\_python: Whether or not to enable the Python registry. | ```object({ location = string enable_npm = bool enable_python = bool })``` | n/a | yes |
| common\_resource\_labels | The common labels that should be applied to all resources that can be labeled. | `map(string)` | n/a | yes |
| domain\_config | n/a | ```object({ nonprod = object({ subdomain_prefix = string zone_visibility = string }) ns_record_ttl = number })``` | n/a | yes |
| gitlab\_runner\_permissions | The GitLab Runner service account, and the roles that should be granted to it. | ```object({ service_account = string roles = list(string) })``` | n/a | yes |
| naming\_root | The root identifier for all Control Tower resources. This value will be combined with the     `environment` to create a unique prefix for any resources requiring a globally-unique name.      This value is intended to be shared across all Control Tower deployments. As of module      creation, this value is "ict". | `string` | n/a | yes |
| project\_id | The ID of the shared services GCP project to deploy to. | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| attestor | The attestor configuration.      Outputs:     - attestor\_name: The name of the attestor.     - kms\_key\_ring\_id: The ID of the KMS key ring used by the attestor. |
| docker\_repository | The Docker repository configuration.      Outputs:     - project: The ID of the project where the Docker repository is located.     - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.     - repository: The name of the Docker repository. |
| generic\_repository | The generic repository configuration.      Outputs:     - project: The ID of the project where the generic repository is located.     - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.     - repository: The name of the generic repository. |
| nonprod\_domain | The domain for the nonprod landing zone. This will serve as the root domain for all nonprod      environments. |
| npm\_repository | The npm repository configuration. Null of the npm repository is not enabled.      Outputs:     - project: The ID of the project where the npm repository is located.     - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.     - repository: The name of the npm repository, if enabled. |
| prod\_domain | The domain for the prod landing zone. This will serve as the root domain for all prod     environments. |
| python\_repository | The Python repository configuration. Null of the Python repository is not enabled.      Outputs:     - project: The ID of the project where the Python repository is located.     - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.     - repository: The name of the Python repository, if enabled. |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
  #     The configuration info for setting up the Artifact Registry repositories. Docker and the
  #     generic site assets repositories are always enabled.
  #
  #     Variables:
  #     - location: The location to deploy the Artifact Registry repositories. Examples: 'us',
  #         'us-central1'.
  #     - enable_npm: Whether or not to enable the npm registry.
  #     - enable_python: Whether or not to enable the Python registry.
  #
  artifact_registry_configuration = ""
  
  # The common labels that should be applied to all resources that can be labeled.
  common_resource_labels = ""
  
  domain_config = ""
  
  # The GitLab Runner service account, and the roles that should be granted to it.
  gitlab_runner_permissions = ""
  
  #     The root identifier for all Control Tower resources. This value will be combined with the
  #     `environment` to create a unique prefix for any resources requiring a globally-unique name.
  #     This value is intended to be shared across all Control Tower deployments. As of module
  #     creation, this value is "ict".
  #
  naming_root = ""
  
  # The ID of the shared services GCP project to deploy to.
  project_id = ""
}
```

<!-- END_TFVARS_DOCS-->
