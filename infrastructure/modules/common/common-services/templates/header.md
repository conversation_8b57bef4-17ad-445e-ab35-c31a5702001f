# Common Services Terraform Module

WIP

## Table of Contents

- [Description](#description)
- [DNS](#dns)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates and manages the services that are used by all Control Tower environments. This includes:

- Docker Image Attestation
- DNS
- Artifact Registry Repositories

## DNS

The `dns.tf` file defines the non-prod and prod DNS managed zones. These will serve as the parent DNS zones to all environments deployed in the non-prod and prod landing zones, respectively. It also creates NS records in the corresponding root Dematic non-prod and prod managed zones to delegate the subdomains to the Control Tower managed zones.

For example: if the DNS name of the non-prod root zone is `dematic.dev.`, then the Control Tower non-prod subdomain's DNS name would be `ict.dematic.dev.`.

Information about the root Dematic non-prod zone is retrieved from the `dematic-domains` Terraform module, which outputs a `data "google_managed_dns_zone"` resource for each of the zones used by Control Tower. For more information about this module, see the README in `infrastructure/modules/data/dematic-domains`.

