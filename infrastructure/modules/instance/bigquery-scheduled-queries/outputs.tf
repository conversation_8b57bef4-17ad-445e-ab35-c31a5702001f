output "scheduled_query_configs" {
  description = "Map of created BigQuery Data Transfer configurations."
  value = {
    for key, config in google_bigquery_data_transfer_config.scheduled_queries : key => {
      id           = config.id
      name         = config.name
      display_name = config.display_name
      location     = config.location
      schedule     = config.schedule
      state        = config.state
    }
  }
}

output "scheduled_query_ids" {
  description = "List of BigQuery Data Transfer configuration IDs."
  value       = [for config in google_bigquery_data_transfer_config.scheduled_queries : config.id]
}
