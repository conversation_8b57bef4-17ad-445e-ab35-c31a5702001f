variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "region" {
  description = "The region to deploy the Cloud Run in."
  type        = string
}

# infrastructure/modules/instance/bigquery-scheduled-queries/locals.tf
locals {
  root_name = var.enforce_unique_naming ? (
    "${var.naming_root}-${var.instance_prefix}-bigquery-scheduler"
  ) : "bigquery-scheduler"
}