# infrastructure/modules/instance/bigquery-scheduled-queries/main.tf
resource "google_project_iam_member" "bigquery_transfer_permissions" {
  project = var.project_id
  role    = "roles/iam.serviceAccountTokenCreator"
  member  = "serviceAccount:service-${var.project_number}@gcp-sa-bigquerydatatransfer.iam.gserviceaccount.com"
}

resource "google_bigquery_data_transfer_config" "scheduled_queries" {
  for_each = var.scheduled_queries
  
  depends_on = [google_project_iam_member.bigquery_transfer_permissions]
  
  display_name           = each.value.display_name
  location               = var.region
  data_source_id         = "scheduled_query"
  schedule               = each.value.schedule
  destination_dataset_id = each.value.destination_dataset_id
  
  params = {
    destination_table_name_template = each.value.destination_table_name
    write_disposition               = each.value.write_disposition
    query                          = each.value.query
  }
}