# Data source for project information
data "google_project" "project" {
  project_id = var.project_id
}

# Service account and IAM roles
resource "google_service_account" "bigquery_scheduler" {
  project      = var.project_id
  account_id   = local.root_name
  display_name = "Service account for BigQuery scheduled queries"
}

resource "google_project_iam_member" "bigquery_scheduler" {
  project  = var.project_id
  for_each = toset(var.required_roles)

  role   = each.value
  member = "serviceAccount:${google_service_account.bigquery_scheduler.email}"
}

# Grant permissions to Google's BigQuery Data Transfer service account
resource "google_project_iam_member" "bigquery_transfer_permissions" {
  project = var.project_id
  role    = "roles/iam.serviceAccountTokenCreator"
  member  = "serviceAccount:service-${data.google_project.project.number}@gcp-sa-bigquerydatatransfer.iam.gserviceaccount.com"
}

# BigQuery scheduled queries
resource "google_bigquery_data_transfer_config" "scheduled_queries" {
  for_each = var.scheduled_queries

  depends_on = [google_project_iam_member.bigquery_transfer_permissions]

  display_name           = each.value.display_name
  location               = var.region
  data_source_id         = "scheduled_query"
  schedule               = each.value.schedule
  destination_dataset_id = each.value.destination_dataset_id

  params = {
    destination_table_name_template = each.value.destination_table_name
    write_disposition               = each.value.write_disposition
    query                           = each.value.query
  }

  labels = var.common_resource_labels
}
