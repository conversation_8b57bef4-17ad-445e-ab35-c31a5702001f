locals {
  # Resource naming
  resource_namespace = "neo4j"

  # Neo4j Aura API configuration
  aura_api_base_url = "https://api.neo4j.io/v1"
  neo4j_credentials = jsondecode(data.google_secret_manager_secret_version.neo4j_client_credentials.secret_data)
  access_token      = jsondecode(data.http.aura_token.response_body).access_token

  # Parse the API response to get instance details including the password
  # Use try() to handle the case where response_body is not available during plan
  neo4j_instance_details = {
    for name in var.neo4j_database_names : name => try(
      jsondecode(data.http.neo4j_instances[name].response_body).data,
      {} # Empty object as fallback during plan
    )
  }
}
