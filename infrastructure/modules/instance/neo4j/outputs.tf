output "neo4j_instances" {
  description = "Details of created Neo4j AuraDB instances"
  value = {
    for name in var.neo4j_database_names : name => {
      instance_id   = try(jsondecode(data.local_file.neo4j_responses[name].content).data.id, null)
      instance_name = name
      uri           = try("neo4j+s://${jsondecode(data.local_file.neo4j_responses[name].content).data.id}.databases.neo4j.io", null)
      secret_id     = google_secret_manager_secret.neo4j_instances[name].secret_id
      status        = try(jsondecode(data.local_file.neo4j_responses[name].content).data.status, null)
    }
  }
}

output "created_secrets" {
  description = "List of GCP Secret Manager secret IDs created for Neo4j instances"
  value = [
    for name in var.neo4j_database_names : google_secret_manager_secret.neo4j_instances[name].secret_id
  ]
}

output "auth_status" {
  description = "Status of the Neo4j Aura API authentication"
  value = {
    status_code = data.http.aura_token.status_code
    success     = data.http.aura_token.status_code == 200
  }
}
