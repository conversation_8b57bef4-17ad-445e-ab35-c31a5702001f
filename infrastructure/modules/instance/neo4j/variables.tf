variable "aura_region" {
  description = "Neo4j Aura region for database instances"
  type        = string
  default     = "us-east1"
}

variable "aura_type" {
  description = "Neo4j Aura type"
  type        = string
  default     = "professional-db"
}

variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
}


variable "instance_memory" {
  description = "Memory allocation for each Neo4j instance"
  type        = string
  default     = "1GB"
}

variable "instance_storage" {
  description = "Storage allocation for each Neo4j instance"
  type        = string
  default     = "2GB"
}

variable "neo4j_database_names" {
  description = "List of database names to create Neo4j instances for"
  type        = list(string)
  default     = []

  validation {
    condition     = length(var.neo4j_database_names) > 0
    error_message = "At least one database name must be provided."
  }

  validation {
    condition = alltrue([
      for dbname in var.neo4j_database_names :
      length(dbname) >= 1 && length(dbname) <= 30
    ])
    error_message = "Database names must be between 1 and 30 characters in length."
  }
}

variable "neo4j_version" {
  description = "Neo4j version for the database instances"
  type        = string
  default     = "5"
}

variable "project_id" {
  description = "The ID of the shared services GCP project to deploy to."
  type        = string
}
