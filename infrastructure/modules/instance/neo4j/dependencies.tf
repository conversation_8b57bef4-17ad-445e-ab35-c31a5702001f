# Get the neo4j client credentials from secret manager
data "google_secret_manager_secret_version" "neo4j_client_credentials" {
  secret  = "auradb_api_client_credentials"
  project = var.project_id
  version = "latest"
}

# Get OAuth token for Neo4j Aura API
data "http" "aura_token" {
  url    = "https://api.neo4j.io/oauth/token"
  method = "POST"

  request_headers = {
    Content-Type  = "application/x-www-form-urlencoded"
    Accept        = "application/json"
    Authorization = "Basic ${base64encode(join(":", [local.neo4j_credentials.CLIENT_ID, local.neo4j_credentials.CLIENT_SECRET]))}"
  }

  request_body = "grant_type=client_credentials"
}
