# Creates a neo4j database instance for each facility specified using the neo4j api
data "http" "neo4j_instances" {
  for_each = toset(var.neo4j_database_names)

  url    = "${local.aura_api_base_url}/instances"
  method = "POST"

  request_headers = {
    Authorization = "Bearer ${local.access_token}"
    Content-Type  = "application/json"
    Accept        = "application/json"
  }

  request_body = jsonencode({
    name           = each.value
    version        = var.neo4j_version # Should be 5
    region         = var.aura_region
    memory         = var.instance_memory
    storage        = var.instance_storage
    cloud_provider = "gcp"
    type           = var.aura_type
    tenant_id      = local.neo4j_credentials.TENANT_ID
  })

}

# Create Secret Manager secrets for each neo4j database instance
# Only create secrets when we have actual instance data (i.e., during apply)
resource "google_secret_manager_secret" "neo4j_instances" {
  for_each = {
    for name in var.neo4j_database_names : name => name
    if can(local.neo4j_instance_details[name].id)
  }

  project   = var.project_id
  secret_id = "${each.value}_AuraDB"

  labels = var.common_resource_labels

  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "neo4j_instances" {
  for_each = {
    for name in var.neo4j_database_names : name => name
    if can(local.neo4j_instance_details[name].id)
  }

  secret = google_secret_manager_secret.neo4j_instances[each.key].id

  secret_data = jsonencode({
    NEO4J_URI         = "neo4j+s://${local.neo4j_instance_details[each.key].id}.databases.neo4j.io"
    NEO4J_USERNAME    = local.neo4j_instance_details[each.key].username
    NEO4J_PASSWORD    = local.neo4j_instance_details[each.key].password
    AURA_INSTANCEID   = local.neo4j_instance_details[each.key].id
    AURA_INSTANCENAME = each.value
  })
}
