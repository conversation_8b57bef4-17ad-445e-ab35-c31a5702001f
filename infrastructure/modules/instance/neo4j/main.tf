# Creates Neo4j AuraDB instances via API calls
resource "null_resource" "neo4j_instances" {
  for_each = toset(var.neo4j_database_names)

  triggers = {
    instance_name = each.value
    version       = var.neo4j_version
    region        = var.aura_region
    memory        = var.instance_memory
    storage       = var.instance_storage
    type          = var.aura_type
    tenant_id     = local.neo4j_credentials.TENANT_ID
  }

  provisioner "local-exec" {
    command = <<EOT
      # Create Neo4j instance via API
      curl -s -X POST "${local.aura_api_base_url}/instances" \
        -H "Authorization: Bearer ${local.access_token}" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d '{
          "name": "${each.value}",
          "version": "${var.neo4j_version}",
          "region": "${var.aura_region}",
          "memory": "${var.instance_memory}",
          "storage": "${var.instance_storage}",
          "cloud_provider": "gcp",
          "type": "${var.aura_type}",
          "tenant_id": "${local.neo4j_credentials.TENANT_ID}"
        }' \
        -o /tmp/neo4j_response_${each.value}.json

      if [ $? -ne 0 ]; then
        echo "Failed to create Neo4j instance ${each.value}"
        echo "Response:"
        cat /tmp/neo4j_response_${each.value}.json
        exit 1
      fi

      echo "Successfully created Neo4j instance ${each.value}"
    EOT
  }

  provisioner "local-exec" {
    when    = destroy
    command = <<EOT
      echo "Note: Deleting of Neo4j Aura instances is not supported here yet. Manual deletion of the instance is required."
    EOT
  }
}

# Read API responses to extract instance details
data "local_file" "neo4j_responses" {
  for_each = toset(var.neo4j_database_names)
  filename = "/tmp/neo4j_response_${each.value}.json"

  depends_on = [null_resource.neo4j_instances]
}

# Create GCP Secret Manager secrets for each Neo4j instance
resource "google_secret_manager_secret" "neo4j_instances" {
  for_each = toset(var.neo4j_database_names)

  project   = var.project_id
  secret_id = "${each.value}_AuraDB"

  labels = var.common_resource_labels

  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "neo4j_instances" {
  for_each = toset(var.neo4j_database_names)

  secret = google_secret_manager_secret.neo4j_instances[each.key].id

  secret_data = jsonencode({
    NEO4J_URI         = try("neo4j+s://${jsondecode(data.local_file.neo4j_responses[each.key].content).data.id}.databases.neo4j.io", "pending")
    NEO4J_USERNAME    = try(jsondecode(data.local_file.neo4j_responses[each.key].content).data.username, "pending")
    NEO4J_PASSWORD    = try(jsondecode(data.local_file.neo4j_responses[each.key].content).data.password, "pending")
    AURA_INSTANCEID   = try(jsondecode(data.local_file.neo4j_responses[each.key].content).data.id, "pending")
    AURA_INSTANCENAME = each.value
    _response_hash    = try(filesha256("/tmp/neo4j_response_${each.key}.json"), "pending")
  })

  depends_on = [data.local_file.neo4j_responses]
}
