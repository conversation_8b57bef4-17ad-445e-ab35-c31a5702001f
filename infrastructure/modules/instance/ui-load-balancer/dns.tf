# TODO: see if we can migrate, but replacing probably won't be an issue

# DNS registration in the environment's primary DNS zone
module "primary_dns_registration" {
  source = "./dns_registration"

  project_id        = var.dns_config.primary_dns_project_id
  managed_zone_name = var.dns_config.primary_dns_zone_name

  a_record_ttl             = var.dns_config.a_record_ttl
  load_balancer_ip_address = google_compute_global_address.main.address
}

# if specified, create an A record that redirects the subdomain to the load balancer's IP
module "alias_dns_registration" {
  count  = var.dns_config.alias_parent_domain_to_self ? 1 : 0
  source = "./dns_registration"

  project_id        = var.dns_config.parent_dns_project_id
  managed_zone_name = var.dns_config.parent_dns_zone_name

  a_record_ttl             = var.dns_config.a_record_ttl
  load_balancer_ip_address = google_compute_global_address.main.address
}