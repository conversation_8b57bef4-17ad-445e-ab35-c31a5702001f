<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# Ignition Proxy Backend Terraform Module

## Table of Contents

- [Description](#description)
- [Details](#details)
- [Enhancements](#enhancements)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates a backend for the front-end load balancer that serves as a proxy to an Ignition host. It creates a Managed Instance Group containing 1 -> n instances running the proxy service, and registers it with a load balancer backend can be connected to a Control Tower front-end load balancer.

## Details

The proxy itself is an NGINX service running on one or more VM instances. The configuration files in the `config` directory are pushed to a GCS bucket. The VMs are described in regional instance templates and managed by a regional Managed Instance Group, and include metadata startup scripts that read the files and configure NGINX on launch. Using the MIG ensures that additional proxy VMs will be distributed among the availability zones in a target region, ensuring that scaling increases both throughput and availability.

The module also creates a service account with the required role grants for the instances to use, and a subnet for the instances.

## Enhancements

- Move the config files into `environments` so that Ignition can be configured differently in each environment

## Usage

Basic usage of this module is as follows:

```hcl
module "ignition-proxy" {
  source  = "gitlab.com/dematic/control-tower-ignition-proxy/google"
  version = "~> 0.1.0"

  # Required variables
  bucket_location        = var.bucket_location
  common_resource_labels = var.common_resource_labels
  enforce_unique_naming  = var.enforce_unique_naming
  host_config            = var.host_config
  host_labels            = var.host_labels
  host_tags              = var.host_tags
  ignition_port          = var.ignition_port
  ignition_upstream      = var.ignition_upstream
  instance_prefix        = var.instance_prefix
  naming_root            = var.naming_root
  network_config         = var.network_config
  project_id             = var.project_id
  region                 = var.region
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |
| random | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| security\_policy | GoogleCloudPlatform/cloud-armor/google | 5.0.0 |

## Resources

| Name | Type |
|------|------|
| [google_compute_backend_service.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_backend_service) | resource |
| [google_compute_health_check.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_health_check) | resource |
| [google_compute_region_instance_group_manager.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_region_instance_group_manager) | resource |
| [google_compute_region_instance_template.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_region_instance_template) | resource |
| [google_compute_resource_policy.snapshots](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_resource_policy) | resource |
| [google_compute_subnetwork.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_subnetwork) | resource |
| [google_project_iam_member.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member) | resource |
| [google_service_account.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_storage_bucket.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket) | resource |
| [google_storage_bucket_object.ignition_upstreams](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket_object) | resource |
| [google_storage_bucket_object.nginx_config](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket_object) | resource |
| [random_string.random](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/string) | resource |
| [google_compute_zones.available_zones](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/compute_zones) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| bucket\_location | The location to deploy the Ignition deployment asset bucket in. | `string` | n/a | yes |
| common\_resource\_labels | The common labels that should be applied to all resources that can be labeled. | `map(string)` | n/a | yes |
| enforce\_unique\_naming | Set this to true to use the provided 'environment` value to create a unique prefix for all      resources in the module. This is required to allow multiple instances of the module to be      deployed in the same GCP project. Used by Review Apps to generate the preview environments. ` | `bool` | n/a | yes |
| host\_config | The configuration for the host GCE instance(s). | ```object({ count = number machine_type = string disk_size = number disk_type = string image = string })``` | n/a | yes |
| host\_labels | The labels that should be applied to the VM host(s), excluding anything already defined in      `common_resource_labels`. | `map(string)` | n/a | yes |
| host\_tags | The tags that should be applied to the VM host(s). This includes things like any network tags      used to control firewall rules, as well as any other tags that are specific to the VM host(s). | `list(string)` | n/a | yes |
| ignition\_port | The port to use on the Ignition service load balancer. | `any` | n/a | yes |
| ignition\_upstream | The upstream URL for the Ignition service. | `string` | n/a | yes |
| instance\_prefix | The name of the instance that the resources are being deployed to. This value will be      combined with the `naming_root` to create a unique prefix for any resources requiring a      globally-unique identifier. Therefore, it must be unique among Control Tower instances but      should be as short as possible to comply with GCP resource name length restrictions. | `string` | n/a | yes |
| naming\_root | The root identifier for all Control Tower resources. This value will be combined with the     `environment` to create a unique prefix for any resources requiring a globally-unique name.      This value is intended to be shared across all Control Tower deployments. As of module      creation, this value is "ict". | `string` | n/a | yes |
| network\_config | Configuration for the network resources to be created.      Note: this module does not validate the flow log configuration beyond ensuring it is not null      if flow logs are enabled. It is intended that the flow log configuration is passed in from the      output of the landing zone module.      See the README and variables.tf of the network submodule of the landing zone module for more      details on the configuration | ```object({ enable_flow_logs = bool network_self_link = string subnet_ip_range = string flow_log_config = optional(object({ aggregation_interval = string flow_sampling = number metadata = string })) })``` | n/a | yes |
| project\_id | The ID of the GCP project to deploy to. | `string` | n/a | yes |
| region | The region to deploy regional resources in. | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| backend\_service\_name | The name of the load balancer backend created for the site asset bucket. |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
  # The location to deploy the Ignition deployment asset bucket in.
  bucket_location = ""
  
  #     The common labels that should be applied to all resources that can be labeled.
  #
  common_resource_labels = ""
  
  #     Set this to true to use the provided 'environment` value to create a unique prefix for all
  #     resources in the module. This is required to allow multiple instances of the module to be
  #     deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  #
  enforce_unique_naming = ""
  
  # The configuration for the host GCE instance(s).
  host_config = ""
  
  #     The labels that should be applied to the VM host(s), excluding anything already defined in
  #     `common_resource_labels`.
  #
  host_labels = ""
  
  #     The tags that should be applied to the VM host(s). This includes things like any network tags
  #     used to control firewall rules, as well as any other tags that are specific to the VM host(s).
  #
  host_tags = ""
  
  # The port to use on the Ignition service load balancer.
  ignition_port = ""
  
  # The upstream URL for the Ignition service.
  ignition_upstream = ""
  
  #     The name of the instance that the resources are being deployed to. This value will be
  #     combined with the `naming_root` to create a unique prefix for any resources requiring a
  #     globally-unique identifier. Therefore, it must be unique among Control Tower instances but
  #     should be as short as possible to comply with GCP resource name length restrictions.
  #
  instance_prefix = ""
  
  #     The root identifier for all Control Tower resources. This value will be combined with the
  #     `environment` to create a unique prefix for any resources requiring a globally-unique name.
  #     This value is intended to be shared across all Control Tower deployments. As of module
  #     creation, this value is "ict".
  #
  naming_root = ""
  
  #     Configuration for the network resources to be created.
  #
  #     Note: this module does not validate the flow log configuration beyond ensuring it is not null
  #     if flow logs are enabled. It is intended that the flow log configuration is passed in from the
  #     output of the landing zone module.
  #
  #     See the README and variables.tf of the network submodule of the landing zone module for more
  #     details on the configuration
  #
  network_config = ""
  
  # The ID of the GCP project to deploy to.
  project_id = ""
  
  # The region to deploy regional resources in.
  region = ""
}
```

<!-- END_TFVARS_DOCS-->
