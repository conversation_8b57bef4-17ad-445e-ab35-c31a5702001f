<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# Redis Terraform Module

## Table of Contents

- [Description](#description)
- [Dependencies](#dependencies)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module configures the Google Memorystore Redis service instance used by the APIs. It also creates a Secret Manager secret containing the connection information.

## Dependencies

This module depends on the [Memorystore](https://registry.terraform.io/modules/terraform-google-modules/memorystore/google/latest) module.

## Usage

Basic usage of this module is as follows:

```hcl
module "redis" {
  source  = "gitlab.com/dematic/control-tower-redis/google"
  version = "~> 0.1.0"

  # Required variables
  alternate_zone         = var.alternate_zone
  common_resource_labels = var.common_resource_labels
  enforce_unique_naming  = var.enforce_unique_naming
  instance_prefix        = var.instance_prefix
  network_self_link      = var.network_self_link
  project_id             = var.project_id
  region                 = var.region
  zone                   = var.zone
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| cache | terraform-google-modules/memorystore/google | ~> 14.0.0 |

## Resources

| Name | Type |
|------|------|
| [google_secret_manager_secret.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret) | resource |
| [google_secret_manager_secret_version.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret_version) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| alternate\_zone | The alernative zone for the Memorystore instance. | `string` | n/a | yes |
| common\_resource\_labels | The common labels that should be applied to all resources that can be labeled. | `map(string)` | n/a | yes |
| enforce\_unique\_naming | Set this to true to use the provided 'environment` value to create a unique prefix for all      resources in the module. This is required to allow multiple instances of the module to be      deployed in the same GCP project. Used by Review Apps to generate the preview environments. ` | `bool` | n/a | yes |
| instance\_prefix | The name of the instance that the resources are being deployed to. This value will be      combined with the `naming_root` to create a unique prefix for any resources requiring a      globally-unique identifier. Therefore, it must be unique among Control Tower instances but      should be as short as possible to comply with GCP resource name length restrictions. | `string` | n/a | yes |
| network\_self\_link | The self-link of the network to register as the Memorystore instance's authorized network. | `string` | n/a | yes |
| project\_id | The ID of the GCP project to deploy to. | `string` | n/a | yes |
| region | The region to deploy regional resources in. | `string` | n/a | yes |
| zone | The zone to deploy zonal resources in. | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| auth\_string | The Redis AUTH string used for authentication. |
| auth\_string\_secret\_name | The name of the secret containing the Redis AUTH string. |
| cache\_id | The ID of the Redis instance. |
| host\_ip | The host IP address of the Redis instance. |
| port | The port number of the Redis instance. |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
  # The alernative zone for the Memorystore instance.
  alternate_zone = ""
  
  # The common labels that should be applied to all resources that can be labeled.
  common_resource_labels = ""
  
  #     Set this to true to use the provided 'environment` value to create a unique prefix for all
  #     resources in the module. This is required to allow multiple instances of the module to be
  #     deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  #
  enforce_unique_naming = ""
  
  #     The name of the instance that the resources are being deployed to. This value will be
  #     combined with the `naming_root` to create a unique prefix for any resources requiring a
  #     globally-unique identifier. Therefore, it must be unique among Control Tower instances but
  #     should be as short as possible to comply with GCP resource name length restrictions.
  #
  instance_prefix = ""
  
  #     The self-link of the network to register as the Memorystore instance's authorized network.
  #
  network_self_link = ""
  
  # The ID of the GCP project to deploy to.
  project_id = ""
  
  # The region to deploy regional resources in.
  region = ""
  
  # The zone to deploy zonal resources in.
  zone = ""
}
```

<!-- END_TFVARS_DOCS-->
