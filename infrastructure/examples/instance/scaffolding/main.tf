module "scaffolding" {
  source  = "gitlab.com/dematic/control-tower-scaffolding/google"
  version = "~> 0.1.0"

  # Required variables
  access_logs_bucket_config = var.access_logs_bucket_config
  additional_api_identities = var.additional_api_identities
  additional_apis           = var.additional_apis
  common_resource_labels    = var.common_resource_labels
  core_services_project_id  = var.core_services_project_id
  domain_config             = var.domain_config
  enforce_unique_naming     = var.enforce_unique_naming
  iap_config                = var.iap_config
  instance_display_name     = var.instance_display_name
  instance_prefix           = var.instance_prefix
  naming_root               = var.naming_root
  network_config            = var.network_config
  project_config            = var.project_config
  project_id                = var.project_id
  project_number            = var.project_number
  region                    = var.region
  snapshots_bucket_config   = var.snapshots_bucket_config

  # Optional variables
  project_role_assignments   = {}
  shared_vpc_host_project_id = null
}