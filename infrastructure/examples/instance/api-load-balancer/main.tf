module "api-load-balancer" {
  source  = "gitlab.com/dematic/control-tower-api-load-balancer/google"
  version = "~> 0.1.0"

  # Required variables
  backend_monitoring_config  = var.backend_monitoring_config
  backend_services           = var.backend_services
  common_resource_labels     = var.common_resource_labels
  default_backend_service_id = var.default_backend_service_id
  dns_config                 = var.dns_config
  enforce_unique_naming      = var.enforce_unique_naming
  instance_display_name      = var.instance_display_name
  instance_prefix            = var.instance_prefix
  load_balancing_scheme      = var.load_balancing_scheme
  minimum_tls_version        = var.minimum_tls_version
  notification_email         = var.notification_email
  project_id                 = var.project_id
  region                     = var.region

  # Optional variables
  alias_parent_domain_to_self = false
}