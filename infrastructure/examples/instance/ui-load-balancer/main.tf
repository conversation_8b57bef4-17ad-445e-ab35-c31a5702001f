module "ui-load-balancer" {
  source  = "gitlab.com/dematic/control-tower-ui-load-balancer/google"
  version = "~> 0.1.0"

  # Required variables
  alert_email_address    = var.alert_email_address
  common_resource_labels = var.common_resource_labels
  dns_config             = var.dns_config
  enforce_unique_naming  = var.enforce_unique_naming
  instance_prefix        = var.instance_prefix
  load_balancing_scheme  = var.load_balancing_scheme
  minimum_tls_version    = var.minimum_tls_version
  project_id             = var.project_id
  region                 = var.region
  site_asset_backend_id  = var.site_asset_backend_id

  # Optional variables
  alias_parent_domain_to_self = false
  proxy_service_backend_id    = null
}