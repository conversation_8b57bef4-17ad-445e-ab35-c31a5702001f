# Global configuration for all Control Tower environments
inputs = {
  billing_account_id  = "00615A-599C36-C77931"
  cost_center         = "550164"
  minimum_tls_version = "TLS_1_2"
  organization_id     = "***********" # dematic.com

  global_default_alternate_zone        = "us-central1-b"
  global_default_load_balancing_scheme = "EXTERNAL_MANAGED"
  global_default_region                = "us-central1"
  global_default_zone                  = "us-central1-a"
  global_naming_root                   = "ict"

  default_postgresql_username = "ict-api"

  gitlab_runner_permissions = {
    service_account = "serviceAccount:<EMAIL>"
    roles = [
      "roles/artifactregistry.reader"
    ]
  }

  global_api_security_roles = [
    "roles/bigquery.user",
    "roles/bigquery.dataViewer",
    "roles/compute.networkUser",
    "roles/secretmanager.secretAccessor",
    "roles/vpcaccess.user",
  ]

  global_data_explorer_config = {
    tables = <<EOT
      dim_container_type,dim_customer_order,dim_device,dim_location,dim_item,dim_module,dim_operator,
      dim_pick_order,dim_reason,dim_status,dim_subsystem,dim_technology,dim_work_area,dim_work_type,
      dim_workstation,dim_zone,fct_fault,fct_inventory,fct_movement,fct_order_line,fct_pick_activity,
      fct_pick_order,fct_wms_customer_order,fct_wms_customer_order_detail
    EOT
  }

  global_default_ip_ranges = {
    api                     = "************/28"
    ignition_proxy          = "************/24"
    metric_processor        = "************/28"
    private_service_connect = "***********"
    typeorm_migrations      = "************/28"
  }

  global_default_backend_cloud_armor_config = {
    module_version = "~> 5.0.0"
    layer_7_ddos_config = {
      enable = true

      default_rule_action = "allow"
      rule_visibility     = "STANDARD"
      json_parsing        = "STANDARD"
      log_level           = "NORMAL"
    }
  }

  global_default_dns_record_ttls = {
    a_record     = 300
    cname_record = 300
    ns_record    = 3600 # 21600
  }

  global_default_vpc_access_connector_config = {
    machine_type  = "e2-micro"
    min_instances = 2
    max_instances = 7
  }

  global_cloud_run_environment_variables = {
    COST_CENTER = "550164"
    CREATED_BY  = "terraform"
  }

  google_health_check_source_ip_ranges = [
    "**********/16",
    "***********/22"
  ]

  google_iap_source_ip_ranges = [
    "************/20"
  ]

  global_oauth_brand_configuration = {
    support_email     = "<EMAIL>"
    application_title = "Control Tower"
  }

  global_project_apis = [
    "billingbudgets.googleapis.com",
    "binaryauthorization.googleapis.com",
    "certificatemanager.googleapis.com",
    "cloudkms.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "cloudscheduler.googleapis.com",
    "compute.googleapis.com",
    "container.googleapis.com",
    "iap.googleapis.com",
    "monitoring.googleapis.com",
    "osconfig.googleapis.com",
    "prod.n4gcp.neo4j.io",
    "pubsub.googleapis.com",
    "recommender.googleapis.com",
    "redis.googleapis.com",
    "run.googleapis.com",
    "serviceusage.googleapis.com",
    "sqladmin.googleapis.com",
    "vpcaccess.googleapis.com",
  ]

  global_project_api_identities = [
    {
      api   = "workflows.googleapis.com"
      roles = []
    },
    {
      api   = "secretmanager.googleapis.com"
      roles = []
    },
    {
      api = "servicenetworking.googleapis.com"
      roles = [
        "roles/compute.networkAdmin",
      ]
    }
  ]

  global_project_labels = {
    lifetime        = "perpetual"
    business-region = "global"
    tenant          = "dematic"
  }

  global_resource_labels = {
    application = "controltower"
    managed_by  = "terraform"
    repository  = "dematic_controltower_control-tower"
  }

  metricprocessor_service_account_roles = [
    "roles/cloudsql.client",
    "roles/compute.networkUser",
    "roles/redis.editor",
    "roles/secretmanager.secretAccessor",
    "roles/vpcaccess.user",
    "roles/pubsub.publisher",
    "roles/run.invoker"
  ]

  bigquery_scheduler_service_account_roles = [
    "roles/bigquery.user",
    "roles/bigquery.dataViewer",
    "roles/bigquery.jobUser",
    "roles/bigquery.dataEditor",
    "roles/bigquery.dataTransfer.editor"
  ]

  tableau_project_apis = [
    "bigquery.googleapis.com",
    "cloudbuild.googleapis.com",
    "cloudfunctions.googleapis.com",
    "cloudscheduler.googleapis.com",
    "dns.googleapis.com",
    "eventarc.googleapis.com",
    "run.googleapis.com",
    "secretmanager.googleapis.com",
    "vpcaccess.googleapis.com",
  ]

  tableau_subdomain_id = "bi"

  iam_bindings = {}
}
