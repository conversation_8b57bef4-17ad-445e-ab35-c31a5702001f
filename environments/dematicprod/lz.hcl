# =====================================
# DematicProd Landing Zone Variables
# =====================================

locals {
  cicd_service_account = "<EMAIL>"
  cicd_service_account_principal = "serviceAccount:${local.cicd_service_account}"
}

inputs = {
  lz_cicd_project_id = "ict-prod-cicd"

  lz_folder_id    = "************" # DematicProd/ControlTower
  lz_folder_name  = "DematicProd"
  lz_short_name   = "prod"
  lz_abbreviation = "p"

  lz_agent_ai_api_url = "" # not yet available for prod
  lz_gold_questions_url = "https://ragengine.dematic.app"
  lz_gold_questions_audience_url = "https://ragengine-***********.us-central1.run.app"

  lz_api_security_roles = []

  lz_cloud_run_environment_variables = {
    PARENT_FOLDER = "DematicProd"
  }

  lz_cors_origin = "*.dematic.cloud"

  lz_default_cloud_run_config = {
    enable_binary_auth = false

    image_tag    = "latest"
    cpu_limit    = 1
    memory_limit = "1024Mi"

    cpu_idle          = true
    startup_cpu_boost = false

    min_instances = 1
    max_instances = 12
  }

  lz_network_flow_log_config = {
    aggregation_interval = "INTERVAL_10_MIN"
    flow_sampling        = 0.7
    metadata             = "INCLUDE_ALL_METADATA"
  }

  lz_project_apis = []

  lz_project_api_identities = []
  lz_project_labels         = {}
  lz_resource_annotations   = {}

  lz_resource_labels = {
    landing_zone = "prod"
  }

  lz_simulation_api_url = "" # not yet avaiable for prod

  iam_bindings = {
    "roles/bigquery.resourceViewer" = [
      "group:<EMAIL>"
    ]
    "roles/iam.serviceAccountUser" = [
      local.cicd_service_account_principal
    ]
    "roles/monitoring.viewer" = [
      "group:<EMAIL>",
      "group:<EMAIL>",
      local.cicd_service_account_principal
    ]
    "roles/secretmanager.viewer" = [
      "group:<EMAIL>",
      "group:<EMAIL>",
      local.cicd_service_account_principal
    ]
  }
}
