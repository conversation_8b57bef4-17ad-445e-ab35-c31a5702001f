module "api_backend" {
  source = "../../../../../infrastructure/modules/instance/api-backend"

  project_id = local.project_id
  region     = var.global_default_region

  cloud_armor_config            = var.global_default_backend_cloud_armor_config
  common_config                 = local.api_backend_common_config
  common_resource_labels        = local.common_resource_labels
  default_config                = local.api_backend_default_config
  enforce_unique_naming         = var.enforce_unique_naming
  instance_display_name         = local.instance_display_name
  instance_prefix               = local.instance_prefix
  load_balancing_scheme         = var.global_default_load_balancing_scheme
  monitoring_notification_email = local.alert_email_address
  naming_root                   = var.global_naming_root
  vpc_access_connector_config   = var.global_default_vpc_access_connector_config

  network_config = {
    enable_flow_logs  = true
    flow_log_config   = var.lz_network_flow_log_config
    network_self_link = module.scaffolding.network_self_link
    subnet_ip_range   = var.global_default_ip_ranges.api
  }

  default_service_name = "mock-data-gen"

  backend_services = {
    admin = {
      service_friendly_name = "Admin API"
      service_type          = "api"
      url_map_path          = "/admin"

      container_image = {
        image_name = "ict-admin-api"
      }

      environment_variables = merge(
        local.api_redis_environment_variables,
        {
          ADMIN_API_URL = local.admin_api_url
          ADMIN_API_KEY = var.admin_api_config.secret_name
        }
      )
    }

    ai = {
      service_friendly_name = "AI API"
      service_type          = "api"
      url_map_path          = "/ai"

      container_image = {
        image_name = "ict-ai-api"
      }

      environment_variables = merge(
        local.api_redis_environment_variables,
        local.api_edp_environment_variables
      )

      required_roles = [
        "roles/bigquery.user",
        "roles/bigquery.dataViewer",
      ]
    }

    dataexplorer = {
      service_friendly_name = "Data Explorer API"
      service_type          = "api"
      url_map_path          = "/data-explorer"

      container_image = {
        image_name = "ict-dataexplorer-api"
      }

      environment_variables = merge(
        local.api_edp_environment_variables,
        local.api_postgresql_environment_variables,
        local.api_redis_environment_variables,
        {
          AGENT_AI_API_URL       = var.lz_agent_ai_api_url
          AIML_DATA_EXPLORER_URL = var.data_explorer_config.url
          GOLD_QUESTIONS_URL     = var.lz_gold_questions_url
          GOLD_QUESTIONS_AUDIENCE_URL = var.lz_gold_questions_audience_url
          AIML_INVOCATION_URL    = var.data_explorer_config.secret_name
          AIML_TABLES            = var.global_data_explorer_config.tables
        }
      )

      required_roles = [
        "roles/aiplatform.user",
        "roles/cloudsql.client",
        "roles/redis.editor",
      ]
    }

    diagnostics = {
      service_friendly_name = "Diagnostics API"
      service_type          = "api"
      url_map_path          = "/diagnostics"

      container_image = {
        image_name = "ict-diagnostics-api"
      }

      environment_variables = merge(
        local.api_redis_environment_variables
      )

      required_roles = [
        "roles/redis.editor",
      ]
    }

    equipment = {
      service_friendly_name = "Equipment API"
      service_type          = "api"
      url_map_path          = "/equipment"

      container_image = {
        image_name = "ict-equipment-api"
      }

      environment_variables = merge(
        local.api_postgresql_environment_variables,
        local.api_redis_environment_variables,
        local.api_edp_environment_variables
      )

      required_roles = [
        "roles/bigquery.user",
        "roles/bigquery.dataViewer",
        "roles/cloudsql.client",
        "roles/redis.editor",
        "roles/storage.objectViewer",
      ]
    }

    inventory = {
      service_friendly_name = "Inventory API"
      service_type          = "api"
      url_map_path          = "/inventory"

      container_image = {
        image_name = "ict-inventory-api"
      }

      environment_variables = merge(
        local.api_postgresql_environment_variables,
        local.api_redis_environment_variables,
        local.api_edp_environment_variables,
        local.api_metric_processor_environment_variables
      )

      required_roles = [
        "roles/bigquery.user",
        "roles/bigquery.dataViewer",
        "roles/cloudsql.client",
        "roles/redis.editor",
        "roles/storage.objectViewer",
      ]
    }

    operators = {
      service_friendly_name = "Operators API"
      service_type          = "api"
      url_map_path          = "/operators"

      container_image = {
        image_name = "ict-operators-api"
      }

      environment_variables = merge(
        local.api_postgresql_environment_variables,
        local.api_redis_environment_variables,
        local.api_edp_environment_variables
      )

      required_roles = [
        "roles/bigquery.user",
        "roles/bigquery.dataViewer",
        "roles/cloudsql.client",
        "roles/redis.editor",
        "roles/storage.objectViewer",
      ]
    }

    orders = {
      service_friendly_name = "Orders API"
      service_type          = "api"
      url_map_path          = "/orders"

      container_image = {
        image_name = "ict-orders-api"
      }

      environment_variables = merge(
        local.api_postgresql_environment_variables,
        local.api_redis_environment_variables,
        local.api_edp_environment_variables
      )
      required_roles = [
        "roles/bigquery.user",
        "roles/bigquery.dataViewer",
        "roles/cloudsql.client",
        "roles/storage.objectViewer",
      ]
    }

    simulation = {
      service_friendly_name = "Simulation API"
      service_type          = "api"
      url_map_path          = "/simulation"

      container_image = {
        image_name = "ict-simulation-api"
      }

      environment_variables = merge(
        local.api_edp_environment_variables,
        local.api_postgresql_environment_variables,
        local.api_redis_environment_variables,
        {
          SIMULATION_API_URL = var.lz_simulation_api_url
          SIMULATION_API_KEY = var.simulation_api_key_secret_name
        }
      )

      required_roles = [
        "roles/cloudsql.client",
        "roles/redis.editor",
      ]
    }

    tableau = {
      service_friendly_name = "Tableau Proxy"
      service_type          = "proxy"
      url_map_path          = "/tableau"

      container_image = {
        image_name = "ict-tableau-proxy"
      }

      environment_variables = {
        TABLEAU_URL = "https://dev.bi.ict.dematic.dev" # TODO: replace with nonprod when ready
      }

      required_roles = [
        "roles/cloudsql.client",
      ]
    }

    workstation = {
      service_friendly_name = "Workstation API"
      service_type          = "api"
      url_map_path          = "/workstation"

      container_image = {
        image_name = "ict-workstation-api"
      }

      environment_variables = merge(
        local.api_postgresql_environment_variables,
        local.api_edp_environment_variables
      )

      required_roles = [
        "roles/bigquery.user",
        "roles/bigquery.dataViewer",
        "roles/cloudsql.client",
      ]
    }
  }

  storage_enabled_backend_services = {
    config = {
      service_friendly_name = "Config API"
      service_type          = "api"
      url_map_path          = "/config"

      bucket_objects = [
        {
          name = "config/app-config/app-config.json"
          # TODO: switch this to the app directory when enabling CI/CD
          object_source_path = (
            var.config_api_config_file_path
          )
          detect_md5hash = true
        }
      ]

      container_image = {
        image_name = "ict-config-api"
      }

      environment_variables = merge(
        local.api_postgresql_environment_variables,
        local.api_redis_environment_variables,
        local.api_edp_environment_variables
      )

      required_roles = [
        "roles/bigquery.connectionUser",
        "roles/bigquery.dataEditor",
        "roles/bigquery.dataViewer",
        "roles/bigquery.jobUser",
        "roles/bigquery.metadataViewer",
        "roles/bigquery.resourceViewer",
        "roles/bigquery.studioUser",
        "roles/bigquery.user",
        "roles/cloudsql.client",
        "roles/redis.editor",
        "roles/storage.admin",
      ]
    }

    mock-data-gen = {
      service_friendly_name = "Mock Data Generator"
      service_type          = "api"
      url_map_path          = "/mock"

      container_image = {
        image_name = "mock-data-api"
      }

      environment_variables = merge(
        local.api_postgresql_environment_variables,
        local.api_redis_environment_variables,
        local.api_edp_environment_variables
      )

      required_roles = [
        "roles/bigquery.user",
        "roles/bigquery.dataViewer",
        "roles/cloudsql.client",
        "roles/compute.networkUser",
        "roles/redis.editor",
        "roles/storage.objectViewer",
        "roles/vpcaccess.user",
      ]
    }
  }
}

module "api_load_balancer" {
  source = "../../../../../infrastructure/modules/instance/api-load-balancer"

  project_id = local.project_id

  common_resource_labels = local.common_resource_labels
  load_balancing_scheme  = var.global_default_load_balancing_scheme
  enforce_unique_naming  = var.enforce_unique_naming
  instance_display_name  = local.instance_display_name
  instance_prefix        = local.instance_prefix
  minimum_tls_version    = var.minimum_tls_version
  notification_email     = local.alert_email_address
  region                 = var.global_default_region

  default_backend_service_id = module.api_backend.default_service_id

  backend_monitoring_config = {
    monitoring = {
      http_check_config = {
        port         = 443
        use_ssl      = true
        validate_ssl = true
      }

      basic_uptime_check_config = {
        period  = "60s"
        timeout = "10s"
      }
      full_uptime_check_config = {
        period  = "60s"
        timeout = "10s"
      }
    }
    alerting = {
      enabled = false
    }
  }

  backend_services = merge(
    module.api_backend.backend_services,
    module.api_backend.storage_enabled_backend_services
  )

  dns_config = {
    alias_parent_domain_to_self = var.alias_parent_domain_to_self

    primary_dns_project_id = data.terraform_remote_state.project.outputs.project_id
    primary_dns_zone_name  = module.scaffolding.dns_zone_name

    a_record_ttl     = var.global_default_dns_record_ttls.a_record
    cname_record_ttl = var.global_default_dns_record_ttls.cname_record
  }

  depends_on = [module.scaffolding]
}
