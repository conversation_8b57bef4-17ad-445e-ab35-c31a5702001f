-- Superior Workstation Daily Performance Query
-- This query aggregates daily workstation performance metrics
-- and creates the superior_workstation_daily_performance table

SELECT
  DATE(event_time) as day,
  workstation_code,
  
  -- Total lines picked
  SUM(CASE WHEN event_code = 'Pick' THEN quantity ELSE 0 END) as lines,
  
  -- Total logged in time in minutes
  SUM(CASE WHEN event_code IN ('Logon', 'Logoff') THEN 
    TIMESTAMP_DIFF(LEAD(event_time) OVER (PARTITION BY workstation_code, DATE(event_time) ORDER BY event_time), 
                   event_time, MINUTE) 
    ELSE 0 END) as total_logged_in_minutes,
  
  -- First shift metrics (6 AM - 2 PM)
  SUM(CASE 
    WHEN event_code = 'Pick' 
    AND EXTRACT(HOUR FROM event_time) BETWEEN 6 AND 13 
    THEN quantity 
    ELSE 0 
  END) as lines_1st_shift,
  
  COUNT(CASE 
    WHEN event_code IN ('Logon', 'Logoff') 
    AND EXTRACT(HOUR FROM event_time) BETWEEN 6 AND 13 
    THEN 1 
  END) / 60.0 as hours_1st_shift,
  
  -- Second shift metrics (2 PM - 10 PM)
  SUM(CASE 
    WHEN event_code = 'Pick' 
    AND EXTRACT(HOUR FROM event_time) BETWEEN 14 AND 21 
    THEN quantity 
    ELSE 0 
  END) as lines_2nd_shift,
  
  COUNT(CASE 
    WHEN event_code IN ('Logon', 'Logoff') 
    AND EXTRACT(HOUR FROM event_time) BETWEEN 14 AND 21 
    THEN 1 
  END) / 60.0 as hours_2nd_shift,
  
  -- Container movements
  SUM(CASE WHEN movement_type = 'DMS_RETRIEVAL' THEN 1 ELSE 0 END) as retrieval_from_dms,
  SUM(CASE WHEN movement_type = 'DMS_STORAGE' THEN 1 ELSE 0 END) as storage_to_dms,
  SUM(CASE WHEN movement_type = 'ASRS_RETRIEVAL' THEN 1 ELSE 0 END) as retrieval_from_asrs,
  SUM(CASE WHEN movement_type = 'ASRS_STORAGE' THEN 1 ELSE 0 END) as storage_to_asrs

FROM `{project_id}.{dataset_id}_gold.pick_activity` 
WHERE DATE(event_time) = DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
  AND workstation_code IS NOT NULL
GROUP BY day, workstation_code
ORDER BY day DESC, workstation_code
